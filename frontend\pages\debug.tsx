import Head from "next/head";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";

export default function Debug() {
  const [termsStatus, setTermsStatus] = useState("checking...");
  const [localStorageWorks, setLocalStorageWorks] = useState("checking...");
  const router = useRouter();

  useEffect(() => {
    // Test localStorage
    try {
      localStorage.setItem("test", "test");
      localStorage.removeItem("test");
      setLocalStorageWorks("✅ Working");
    } catch (error) {
      setLocalStorageWorks("❌ Not working: " + error);
    }

    // Check terms status
    try {
      const terms = localStorage.getItem("terms_accepted");
      setTermsStatus(terms ? `✅ Accepted: ${terms}` : "❌ Not accepted");
    } catch (error) {
      setTermsStatus("❌ Error: " + error);
    }
  }, []);

  const clearTerms = () => {
    try {
      localStorage.removeItem("terms_accepted");
      setTermsStatus("❌ Cleared");
    } catch (error) {
      setTermsStatus("❌ Error clearing: " + error);
    }
  };

  const acceptTerms = () => {
    try {
      localStorage.setItem("terms_accepted", "true");
      setTermsStatus("✅ Accepted: true");
    } catch (error) {
      setTermsStatus("❌ Error setting: " + error);
    }
  };

  return (
    <>
      <Head>
        <title>Ghostify - Debug</title>
      </Head>
      
      <div className="app-container">
        <div className="main-content">
          <div style={{ 
            background: 'rgba(255,255,255,0.95)', 
            padding: '2rem', 
            borderRadius: '16px',
            maxWidth: '600px',
            margin: '2rem auto'
          }}>
            <h1 style={{ color: '#2d3748', marginBottom: '2rem' }}>Debug Information</h1>
            
            <div style={{ marginBottom: '1rem' }}>
              <strong>LocalStorage Status:</strong> {localStorageWorks}
            </div>
            
            <div style={{ marginBottom: '2rem' }}>
              <strong>Terms Status:</strong> {termsStatus}
            </div>
            
            <div style={{ display: 'flex', gap: '1rem', marginBottom: '2rem' }}>
              <button 
                onClick={clearTerms}
                style={{
                  background: '#f56565',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Clear Terms
              </button>
              
              <button 
                onClick={acceptTerms}
                style={{
                  background: '#48bb78',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Accept Terms
              </button>
            </div>
            
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button 
                onClick={() => router.push("/")}
                style={{
                  background: '#667eea',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Go to Home
              </button>
              
              <button 
                onClick={() => router.push("/terms")}
                style={{
                  background: '#667eea',
                  color: 'white',
                  border: 'none',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}
              >
                Go to Terms
              </button>
            </div>
            
            <div style={{ marginTop: '2rem', padding: '1rem', background: '#f7fafc', borderRadius: '8px' }}>
              <h3 style={{ color: '#2d3748', marginBottom: '1rem' }}>Expected Flow:</h3>
              <ol style={{ color: '#4a5568' }}>
                <li>Visit home page (/) - should redirect to /terms if not accepted</li>
                <li>Accept terms on /terms page</li>
                <li>Get redirected back to home page</li>
                <li>Fill out form and submit</li>
                <li>Get redirected to /result page</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
