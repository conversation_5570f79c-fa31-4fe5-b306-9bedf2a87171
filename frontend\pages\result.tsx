import Head from "next/head";
import { useState, useEffect } from "react";
import ScanStatus from "../components/ScanStatus";
import ScanResults from "../components/ScanResults";
import ReportDownload from "../components/ReportDownload";

export default function ResultPage() {
  const [searchId, setSearchId] = useState<string | null>(null);
  const [scanStatus, setScanStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const storedSearchId = localStorage.getItem("search_id");
    if (storedSearchId) {
      setSearchId(storedSearchId);
      fetchScanStatus(storedSearchId);
    } else {
      setIsLoading(false);
    }
  }, []);

  const fetchScanStatus = async (id: string) => {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE || "http://localhost:8000";
      const response = await fetch(`${apiBase}/api/scan/${id}/status`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Ensure numeric values are properly handled
      if (data.exposure_score !== undefined && data.exposure_score !== null) {
        data.exposure_score = Number(data.exposure_score);
      }

      setScanStatus(data);

      // If scan is still running, poll for updates
      if (data.status === "running" || data.status === "pending") {
        setTimeout(() => fetchScanStatus(id), 5000); // Poll every 5 seconds
      }
    } catch (error) {
      console.error("Error fetching scan status:", error);
      setScanStatus({
        search_id: id,
        status: "error",
        findings_count: 0,
        created_at: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <>
        <Head>
          <title>Ghostify - Loading Results</title>
        </Head>
        <div className="container">
          <h1 className="title">Loading...</h1>
          <p className="subtitle">Please wait while we fetch your results.</p>
        </div>
      </>
    );
  }

  if (!searchId || !scanStatus) {
    return (
      <>
        <Head>
          <title>Ghostify - No Results</title>
        </Head>
        <div className="container">
          <h1 className="title">No Scan Found</h1>
          <p className="subtitle">Please start a new scan.</p>
          <a href="/" className="button">Start New Scan</a>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Ghostify - Scan Results</title>
      </Head>
      <div className="container">
        <h1 className="title">OSINT Scan Results</h1>

        <ScanStatus status={scanStatus} />

        {scanStatus.status === "completed" && (
          <>
            <ScanResults searchId={searchId} />
            <ReportDownload searchId={searchId} />
          </>
        )}

        {(scanStatus.status === "failed" || scanStatus.status === "error") && (
          <div className="error-message">
            <p>Scan failed or encountered an error. Please try again.</p>
            <a href="/" className="button">Start New Scan</a>
          </div>
        )}
      </div>
    </>
  );
}
