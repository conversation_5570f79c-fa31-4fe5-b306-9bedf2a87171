<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <!-- CyberDucky Logo -->
  <defs>
    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffff00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8c00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="200" cy="200" r="190" fill="none" stroke="#00ffe7" stroke-width="2" opacity="0.3"/>
  
  <!-- CyberDucky text curved at top -->
  <path id="textcircle" d="M 50,200 A 150,150 0 0,1 350,200" fill="none"/>
  <text font-family="Arial Black, sans-serif" font-size="32" font-weight="bold" fill="url(#blueGradient)">
    <textPath href="#textcircle" startOffset="50%" text-anchor="middle">
      CyberDucky
    </textPath>
  </text>
  
  <!-- Duck body (striped/segmented effect) -->
  <g transform="translate(200,250)">
    <!-- Main body segments -->
    <ellipse cx="0" cy="0" rx="80" ry="60" fill="url(#yellowGradient)" opacity="0.9"/>
    <ellipse cx="0" cy="-5" rx="75" ry="55" fill="#ffff00" opacity="0.8"/>
    <ellipse cx="0" cy="-10" rx="70" ry="50" fill="url(#yellowGradient)" opacity="0.9"/>
    <ellipse cx="0" cy="-15" rx="65" ry="45" fill="#ffff00" opacity="0.8"/>
    <ellipse cx="0" cy="-20" rx="60" ry="40" fill="url(#yellowGradient)" opacity="0.9"/>
    <ellipse cx="0" cy="-25" rx="55" ry="35" fill="#ffff00" opacity="0.8"/>
    <ellipse cx="0" cy="-30" rx="50" ry="30" fill="url(#yellowGradient)" opacity="0.9"/>
    <ellipse cx="0" cy="-35" rx="45" ry="25" fill="#ffff00" opacity="0.8"/>
    <ellipse cx="0" cy="-40" rx="40" ry="20" fill="url(#yellowGradient)" opacity="0.9"/>
  </g>
  
  <!-- Duck head -->
  <g transform="translate(200,160)">
    <circle cx="0" cy="0" r="35" fill="url(#yellowGradient)"/>
    <!-- Beak -->
    <ellipse cx="25" cy="5" rx="15" ry="8" fill="#ff8c00"/>
    <!-- Eye -->
    <circle cx="10" cy="-10" r="6" fill="#000"/>
    <circle cx="12" cy="-12" r="2" fill="#fff"/>
    <!-- Cyber enhancement on eye -->
    <rect x="5" y="-15" width="12" height="3" fill="#00ffe7" opacity="0.8"/>
    <rect x="7" y="-18" width="8" height="2" fill="#00ffe7" opacity="0.6"/>
  </g>
  
  <!-- Wing -->
  <g transform="translate(160,200)">
    <ellipse cx="0" cy="0" rx="25" ry="40" fill="url(#yellowGradient)" opacity="0.7"/>
    <!-- Wing segments -->
    <line x1="-20" y1="-30" x2="20" y2="-20" stroke="#ff8c00" stroke-width="2"/>
    <line x1="-20" y1="-15" x2="20" y2="-5" stroke="#ff8c00" stroke-width="2"/>
    <line x1="-20" y1="0" x2="20" y2="10" stroke="#ff8c00" stroke-width="2"/>
    <line x1="-20" y1="15" x2="20" y2="25" stroke="#ff8c00" stroke-width="2"/>
  </g>
  
  <!-- Cyber elements -->
  <g transform="translate(200,200)">
    <!-- Circuit lines -->
    <path d="M -60,-40 L -40,-40 L -40,-20 L -20,-20" stroke="#00ffe7" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 60,-30 L 40,-30 L 40,-10 L 20,-10" stroke="#00ffe7" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M -50,40 L -30,40 L -30,20 L -10,20" stroke="#00ffe7" stroke-width="2" fill="none" opacity="0.6"/>
    
    <!-- Circuit nodes -->
    <circle cx="-60" cy="-40" r="3" fill="#00ffe7"/>
    <circle cx="60" cy="-30" r="3" fill="#00ffe7"/>
    <circle cx="-50" cy="40" r="3" fill="#00ffe7"/>
    <circle cx="-20" cy="-20" r="2" fill="#00ffe7"/>
    <circle cx="20" cy="-10" r="2" fill="#00ffe7"/>
    <circle cx="-10" cy="20" r="2" fill="#00ffe7"/>
  </g>
  
  <!-- Glitch effect lines -->
  <g opacity="0.4">
    <rect x="150" y="180" width="100" height="1" fill="#ff0080"/>
    <rect x="160" y="220" width="80" height="1" fill="#ff0080"/>
    <rect x="170" y="260" width="60" height="1" fill="#ff0080"/>
  </g>
</svg>
