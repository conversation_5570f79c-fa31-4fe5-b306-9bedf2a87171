"""
Ghostify Connector Framework - Base Classes
Inspired by Maltego's Transform architecture for extensible OSINT data collection
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import asyncio
import logging

logger = logging.getLogger(__name__)


class EntityType(Enum):
    """Standard entity types for OSINT investigations"""
    PERSON = "person"
    EMAIL = "email"
    USERNAME = "username"
    PHONE = "phone"
    DOMAIN = "domain"
    IP_ADDRESS = "ip_address"
    LOCATION = "location"
    SOCIAL_MEDIA_ACCOUNT = "social_media_account"
    DOCUMENT = "document"
    IMAGE = "image"
    URL = "url"
    COMPANY = "company"
    CRYPTOCURRENCY_ADDRESS = "crypto_address"


class RiskLevel(Enum):
    """Risk levels for findings"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ConnectorEntity:
    """Represents a data entity in the OSINT investigation"""
    entity_type: EntityType
    value: str
    display_name: Optional[str] = None
    properties: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 1.0  # 0.0 to 1.0
    source: Optional[str] = None
    
    def __post_init__(self):
        if self.display_name is None:
            self.display_name = self.value


@dataclass
class ConnectorResult:
    """Standardized result from a connector execution"""
    source_entity: ConnectorEntity
    target_entities: List[ConnectorEntity]
    metadata: Dict[str, Any] = field(default_factory=dict)
    risk_level: RiskLevel = RiskLevel.LOW
    confidence: float = 1.0
    execution_time: float = 0.0
    found_at: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def entity_count(self) -> int:
        return len(self.target_entities)


@dataclass
class ConnectorConfig:
    """Configuration for a connector"""
    name: str
    description: str
    version: str
    author: str
    enabled: bool = True
    rate_limit: int = 60  # requests per minute
    timeout: int = 30  # seconds
    requires_api_key: bool = False
    api_key_name: Optional[str] = None
    supported_input_types: List[EntityType] = field(default_factory=list)
    output_types: List[EntityType] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)


class ConnectorError(Exception):
    """Base exception for connector errors"""
    def __init__(self, message: str, connector_name: str = None, original_error: Exception = None):
        self.message = message
        self.connector_name = connector_name
        self.original_error = original_error
        super().__init__(self.message)


class BaseConnector(ABC):
    """
    Base class for all OSINT connectors
    Inspired by Maltego's Transform architecture
    """
    
    def __init__(self, config: ConnectorConfig):
        self.config = config
        self.logger = logging.getLogger(f"connector.{config.name}")
        self._last_request_time = None
        self._request_count = 0
        
    @abstractmethod
    async def execute(self, entity: ConnectorEntity, **kwargs) -> ConnectorResult:
        """
        Execute the connector with the given entity
        
        Args:
            entity: Input entity to process
            **kwargs: Additional parameters
            
        Returns:
            ConnectorResult with discovered entities
        """
        pass
    
    @abstractmethod
    def can_process(self, entity: ConnectorEntity) -> bool:
        """
        Check if this connector can process the given entity type
        
        Args:
            entity: Entity to check
            
        Returns:
            True if connector can process this entity
        """
        pass
    
    async def validate_input(self, entity: ConnectorEntity) -> bool:
        """
        Validate input entity before processing
        
        Args:
            entity: Entity to validate
            
        Returns:
            True if entity is valid for processing
        """
        if not self.can_process(entity):
            return False
            
        if entity.entity_type not in self.config.supported_input_types:
            return False
            
        return True
    
    async def rate_limit_check(self):
        """Check and enforce rate limiting"""
        current_time = datetime.utcnow()
        
        if self._last_request_time:
            time_diff = (current_time - self._last_request_time).total_seconds()
            if time_diff < (60 / self.config.rate_limit):
                sleep_time = (60 / self.config.rate_limit) - time_diff
                await asyncio.sleep(sleep_time)
        
        self._last_request_time = current_time
        self._request_count += 1
    
    def create_entity(self, entity_type: EntityType, value: str, **properties) -> ConnectorEntity:
        """
        Helper method to create a new entity
        
        Args:
            entity_type: Type of entity to create
            value: Primary value of the entity
            **properties: Additional properties
            
        Returns:
            New ConnectorEntity instance
        """
        return ConnectorEntity(
            entity_type=entity_type,
            value=value,
            properties=properties,
            source=self.config.name
        )
    
    def create_result(self, source_entity: ConnectorEntity, target_entities: List[ConnectorEntity], 
                     **metadata) -> ConnectorResult:
        """
        Helper method to create a connector result
        
        Args:
            source_entity: Input entity
            target_entities: Discovered entities
            **metadata: Additional metadata
            
        Returns:
            New ConnectorResult instance
        """
        return ConnectorResult(
            source_entity=source_entity,
            target_entities=target_entities,
            metadata=metadata
        )


class ConnectorRegistry:
    """Registry for managing available connectors"""
    
    def __init__(self):
        self._connectors: Dict[str, Type[BaseConnector]] = {}
        self._instances: Dict[str, BaseConnector] = {}
    
    def register(self, connector_class: Type[BaseConnector]):
        """Register a connector class"""
        # Get config from class
        if hasattr(connector_class, 'get_config'):
            config = connector_class.get_config()
            self._connectors[config.name] = connector_class
            logger.info(f"Registered connector: {config.name}")
        else:
            raise ConnectorError(f"Connector {connector_class.__name__} must implement get_config() method")
    
    def get_connector(self, name: str) -> Optional[BaseConnector]:
        """Get a connector instance by name"""
        if name in self._instances:
            return self._instances[name]
        
        if name in self._connectors:
            connector_class = self._connectors[name]
            config = connector_class.get_config()
            instance = connector_class(config)
            self._instances[name] = instance
            return instance
        
        return None
    
    def list_connectors(self) -> List[ConnectorConfig]:
        """List all registered connectors"""
        configs = []
        for connector_class in self._connectors.values():
            configs.append(connector_class.get_config())
        return configs
    
    def get_connectors_for_entity(self, entity_type: EntityType) -> List[BaseConnector]:
        """Get all connectors that can process the given entity type"""
        connectors = []
        for name, connector_class in self._connectors.items():
            config = connector_class.get_config()
            if entity_type in config.supported_input_types:
                connector = self.get_connector(name)
                if connector:
                    connectors.append(connector)
        return connectors


# Global registry instance
registry = ConnectorRegistry()
