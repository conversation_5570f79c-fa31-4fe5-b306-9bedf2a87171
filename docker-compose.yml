services:
  database:
    image: postgres:15
    container_name: ghostify-db
    environment:
      POSTGRES_DB: ghostify
      POSTGRES_USER: ghostify
      POSTGRES_PASSWORD: ghostify_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: always

  backend:
    build: ./backend
    container_name: ghostify-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*****************************************************/ghostify
      - SECRET_KEY=your-secret-key-change-this-in-production
    volumes:
      - ./backend:/app
      - /tmp:/tmp
    depends_on:
      - database
    restart: always

  frontend:
    build: ./frontend
    container_name: ghostify-frontend
    ports:
      - "3001:3000"
    environment:
      - NEXT_PUBLIC_API_BASE=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    restart: always

volumes:
  postgres_data:
