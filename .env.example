# Ghostify Environment Configuration
# Copy this file to .env and update the values

# Database Configuration
DATABASE_URL=*****************************************************/ghostify

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production-make-it-long-and-random

# Optional API Keys for Enhanced Functionality
# Get these from the respective services for better results

# HaveIBeenPwned API Key (Recommended)
# Visit: https://haveibeenpwned.com/API/Key
# Cost: ~$3.50/month for unlimited requests
HIBP_API_KEY=

# EmailRep API Key (Optional)
# Visit: https://emailrep.io/
# Free tier: 300 requests/day
EMAILREP_API_KEY=

# Additional Configuration (Advanced)
# Uncomment and modify as needed

# Rate Limiting (requests per minute)
# DEFAULT_RATE_LIMIT=60

# Logging Level
# LOG_LEVEL=INFO

# Report Storage Path
# REPORTS_PATH=/tmp/reports

# Maximum file upload size (MB)
# MAX_UPLOAD_SIZE=10
