#!/bin/bash

# Ghostify API Test Script
# This script tests the main API endpoints to ensure everything is working

echo "🦆 Ghostify API Test Script"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# API Base URL
API_BASE="http://localhost:8000"
FRONTEND_BASE="http://localhost:3001"

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /dev/null "$API_BASE$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -o /dev/null -X "$method" "$API_BASE$endpoint")
    fi
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $response)"
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $response, expected $expected_status)"
    fi
}

# Function to test scan endpoint
test_scan() {
    echo -n "Testing OSINT scan creation... "
    
    response=$(curl -s -X POST "$API_BASE/api/scan" \
        -F "email=<EMAIL>" \
        -F "name=Test User" \
        -F "username=testuser")
    
    if echo "$response" | grep -q "search_id"; then
        search_id=$(echo "$response" | grep -o '"search_id":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✓ PASS${NC} (Search ID: ${search_id:0:8}...)"
        
        # Test status endpoint
        echo -n "Testing scan status check... "
        status_response=$(curl -s "$API_BASE/api/scan/$search_id/status")
        if echo "$status_response" | grep -q "status"; then
            echo -e "${GREEN}✓ PASS${NC}"
        else
            echo -e "${RED}✗ FAIL${NC}"
        fi
        
        # Test results endpoint
        echo -n "Testing scan results... "
        results_response=$(curl -s "$API_BASE/api/scan/$search_id/results")
        if echo "$results_response" | grep -q "search"; then
            echo -e "${GREEN}✓ PASS${NC}"
        else
            echo -e "${RED}✗ FAIL${NC}"
        fi
    else
        echo -e "${RED}✗ FAIL${NC}"
        echo "Response: $response"
    fi
}

# Function to check container status
check_containers() {
    echo "Checking Docker containers..."
    
    containers=("ghostify-backend" "ghostify-frontend" "ghostify-db")
    
    for container in "${containers[@]}"; do
        echo -n "  $container: "
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{print $2}')
            echo -e "${GREEN}✓ Running${NC} ($status)"
        else
            echo -e "${RED}✗ Not running${NC}"
        fi
    done
}

# Function to check ports
check_ports() {
    echo "Checking port accessibility..."
    
    ports=("8000:Backend API" "3001:Frontend" "5432:Database")
    
    for port_info in "${ports[@]}"; do
        port=$(echo "$port_info" | cut -d':' -f1)
        service=$(echo "$port_info" | cut -d':' -f2)
        echo -n "  Port $port ($service): "
        
        if nc -z localhost "$port" 2>/dev/null; then
            echo -e "${GREEN}✓ Open${NC}"
        else
            echo -e "${RED}✗ Closed${NC}"
        fi
    done
}

# Main test execution
echo
check_containers
echo
check_ports
echo

echo "Testing API endpoints..."
test_endpoint "GET" "/" "200" "Root endpoint"
test_endpoint "GET" "/health" "200" "Health check"
test_endpoint "GET" "/docs" "200" "API documentation"

echo
test_scan

echo
echo "Testing frontend..."
echo -n "Frontend accessibility: "
frontend_response=$(curl -s -w "%{http_code}" -o /dev/null "$FRONTEND_BASE")
if [ "$frontend_response" = "200" ]; then
    echo -e "${GREEN}✓ PASS${NC} (HTTP $frontend_response)"
else
    echo -e "${RED}✗ FAIL${NC} (HTTP $frontend_response)"
fi

echo
echo "🎉 Test completed!"
echo
echo "If any tests failed, try:"
echo "  1. docker-compose down && docker-compose up -d"
echo "  2. docker-compose logs [service-name]"
echo "  3. Check the README.md for troubleshooting"
