"""
Entity definitions for the Ghostify Connector Framework
"""

from .base import ConnectorEntity, EntityType
from typing import Optional, Dict, Any


class PersonEntity(ConnectorEntity):
    """Represents a person entity"""
    
    def __init__(self, name: str, **properties):
        super().__init__(
            entity_type=EntityType.PERSON,
            value=name,
            display_name=name,
            properties=properties
        )
    
    @property
    def first_name(self) -> Optional[str]:
        return self.properties.get('first_name')
    
    @property
    def last_name(self) -> Optional[str]:
        return self.properties.get('last_name')
    
    @property
    def age(self) -> Optional[int]:
        return self.properties.get('age')
    
    @property
    def location(self) -> Optional[str]:
        return self.properties.get('location')


class EmailEntity(ConnectorEntity):
    """Represents an email address entity"""
    
    def __init__(self, email: str, **properties):
        super().__init__(
            entity_type=EntityType.EMAIL,
            value=email,
            display_name=email,
            properties=properties
        )
    
    @property
    def domain(self) -> str:
        return self.value.split('@')[1] if '@' in self.value else ''
    
    @property
    def username(self) -> str:
        return self.value.split('@')[0] if '@' in self.value else self.value
    
    @property
    def is_disposable(self) -> Optional[bool]:
        return self.properties.get('is_disposable')
    
    @property
    def reputation_score(self) -> Optional[float]:
        return self.properties.get('reputation_score')


class UsernameEntity(ConnectorEntity):
    """Represents a username entity"""
    
    def __init__(self, username: str, **properties):
        super().__init__(
            entity_type=EntityType.USERNAME,
            value=username,
            display_name=username,
            properties=properties
        )
    
    @property
    def platform(self) -> Optional[str]:
        return self.properties.get('platform')
    
    @property
    def profile_url(self) -> Optional[str]:
        return self.properties.get('profile_url')
    
    @property
    def verified(self) -> Optional[bool]:
        return self.properties.get('verified')


class PhoneEntity(ConnectorEntity):
    """Represents a phone number entity"""
    
    def __init__(self, phone: str, **properties):
        super().__init__(
            entity_type=EntityType.PHONE,
            value=phone,
            display_name=phone,
            properties=properties
        )
    
    @property
    def country_code(self) -> Optional[str]:
        return self.properties.get('country_code')
    
    @property
    def carrier(self) -> Optional[str]:
        return self.properties.get('carrier')
    
    @property
    def location(self) -> Optional[str]:
        return self.properties.get('location')
    
    @property
    def number_type(self) -> Optional[str]:
        return self.properties.get('number_type')


class DomainEntity(ConnectorEntity):
    """Represents a domain entity"""
    
    def __init__(self, domain: str, **properties):
        super().__init__(
            entity_type=EntityType.DOMAIN,
            value=domain,
            display_name=domain,
            properties=properties
        )
    
    @property
    def registrar(self) -> Optional[str]:
        return self.properties.get('registrar')
    
    @property
    def creation_date(self) -> Optional[str]:
        return self.properties.get('creation_date')
    
    @property
    def expiration_date(self) -> Optional[str]:
        return self.properties.get('expiration_date')


class IPAddressEntity(ConnectorEntity):
    """Represents an IP address entity"""
    
    def __init__(self, ip_address: str, **properties):
        super().__init__(
            entity_type=EntityType.IP_ADDRESS,
            value=ip_address,
            display_name=ip_address,
            properties=properties
        )
    
    @property
    def country(self) -> Optional[str]:
        return self.properties.get('country')
    
    @property
    def city(self) -> Optional[str]:
        return self.properties.get('city')
    
    @property
    def isp(self) -> Optional[str]:
        return self.properties.get('isp')
    
    @property
    def is_vpn(self) -> Optional[bool]:
        return self.properties.get('is_vpn')


class LocationEntity(ConnectorEntity):
    """Represents a location entity"""
    
    def __init__(self, location: str, **properties):
        super().__init__(
            entity_type=EntityType.LOCATION,
            value=location,
            display_name=location,
            properties=properties
        )
    
    @property
    def latitude(self) -> Optional[float]:
        return self.properties.get('latitude')
    
    @property
    def longitude(self) -> Optional[float]:
        return self.properties.get('longitude')
    
    @property
    def country(self) -> Optional[str]:
        return self.properties.get('country')
    
    @property
    def city(self) -> Optional[str]:
        return self.properties.get('city')


class SocialMediaAccountEntity(ConnectorEntity):
    """Represents a social media account entity"""
    
    def __init__(self, username: str, platform: str, **properties):
        super().__init__(
            entity_type=EntityType.SOCIAL_MEDIA_ACCOUNT,
            value=f"{platform}:{username}",
            display_name=f"{username} on {platform}",
            properties={**properties, 'platform': platform, 'username': username}
        )
    
    @property
    def platform(self) -> str:
        return self.properties.get('platform', '')
    
    @property
    def username(self) -> str:
        return self.properties.get('username', '')
    
    @property
    def profile_url(self) -> Optional[str]:
        return self.properties.get('profile_url')
    
    @property
    def followers_count(self) -> Optional[int]:
        return self.properties.get('followers_count')
    
    @property
    def verified(self) -> Optional[bool]:
        return self.properties.get('verified')


class DocumentEntity(ConnectorEntity):
    """Represents a document entity"""
    
    def __init__(self, url: str, **properties):
        super().__init__(
            entity_type=EntityType.DOCUMENT,
            value=url,
            display_name=properties.get('title', url),
            properties=properties
        )
    
    @property
    def title(self) -> Optional[str]:
        return self.properties.get('title')
    
    @property
    def file_type(self) -> Optional[str]:
        return self.properties.get('file_type')
    
    @property
    def size(self) -> Optional[int]:
        return self.properties.get('size')


class ImageEntity(ConnectorEntity):
    """Represents an image entity"""
    
    def __init__(self, url: str, **properties):
        super().__init__(
            entity_type=EntityType.IMAGE,
            value=url,
            display_name=properties.get('alt_text', url),
            properties=properties
        )
    
    @property
    def alt_text(self) -> Optional[str]:
        return self.properties.get('alt_text')
    
    @property
    def width(self) -> Optional[int]:
        return self.properties.get('width')
    
    @property
    def height(self) -> Optional[int]:
        return self.properties.get('height')
    
    @property
    def file_size(self) -> Optional[int]:
        return self.properties.get('file_size')
