import aiohttp
import logging
from typing import List, Dict, Any
from datetime import datetime
from app.osint.base import OSINTCollector, OSINTResult, SearchTarget
import os

logger = logging.getLogger(__name__)

class HaveIBeenPwnedCollector(OSINTCollector):
    """Collector for HaveIBeenPwned breach data."""
    
    def __init__(self):
        super().__init__("HaveIBeenPwned", "breach_database")
        self.api_key = os.getenv("HIBP_API_KEY")
        self.base_url = "https://haveibeenpwned.com/api/v3"
        self.rate_limit = 1  # 1 request per 1.5 seconds for free tier
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Search for email breaches."""
        results = []
        
        if not target.email:
            return results
        
        if not self.api_key:
            logger.warning("HIBP API key not configured")
            return results
        
        try:
            await self.rate_limit_check()
            
            # Check for breaches
            breach_results = await self._check_breaches(target.email)
            results.extend(breach_results)
            
            # Check for pastes
            paste_results = await self._check_pastes(target.email)
            results.extend(paste_results)
            
        except Exception as e:
            logger.error(f"Error checking HIBP for {target.email}: {str(e)}")
        
        return results
    
    async def _check_breaches(self, email: str) -> List[OSINTResult]:
        """Check for data breaches."""
        results = []
        
        headers = {
            "hibp-api-key": self.api_key,
            "User-Agent": "Ghostify-OSINT-Tool"
        }
        
        url = f"{self.base_url}/breachedaccount/{email}"
        
        try:
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    breaches = await response.json()
                    
                    for breach in breaches:
                        result = OSINTResult(
                            source="HaveIBeenPwned",
                            platform="breach_database",
                            finding_type="data_breach",
                            title=f"Data Breach: {breach.get('Name', 'Unknown')}",
                            description=f"Email found in {breach.get('Name')} breach. "
                                      f"Breach date: {breach.get('BreachDate')}. "
                                      f"Compromised data: {', '.join(breach.get('DataClasses', []))}",
                            url=f"https://haveibeenpwned.com/account/{email}",
                            metadata={
                                "breach_name": breach.get("Name"),
                                "breach_date": breach.get("BreachDate"),
                                "pwn_count": breach.get("PwnCount"),
                                "data_classes": breach.get("DataClasses", []),
                                "is_verified": breach.get("IsVerified", False),
                                "is_sensitive": breach.get("IsSensitive", False)
                            },
                            risk_score=self._calculate_breach_risk(breach),
                            confidence=0.9 if breach.get("IsVerified") else 0.7,
                            found_at=datetime.now()
                        )
                        results.append(result)
                
                elif response.status == 404:
                    # No breaches found - this is good news
                    logger.info(f"No breaches found for {email}")
                else:
                    logger.error(f"HIBP API error: {response.status}")
        
        except Exception as e:
            logger.error(f"Error checking breaches: {str(e)}")
        
        return results
    
    async def _check_pastes(self, email: str) -> List[OSINTResult]:
        """Check for paste site mentions."""
        results = []
        
        headers = {
            "hibp-api-key": self.api_key,
            "User-Agent": "Ghostify-OSINT-Tool"
        }
        
        url = f"{self.base_url}/pasteaccount/{email}"
        
        try:
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    pastes = await response.json()
                    
                    for paste in pastes:
                        result = OSINTResult(
                            source="HaveIBeenPwned",
                            platform="paste_sites",
                            finding_type="paste_mention",
                            title=f"Paste Site Mention: {paste.get('Source', 'Unknown')}",
                            description=f"Email found in paste on {paste.get('Source')}. "
                                      f"Date: {paste.get('Date')}",
                            url=paste.get("Id"),
                            metadata={
                                "source": paste.get("Source"),
                                "paste_id": paste.get("Id"),
                                "date": paste.get("Date"),
                                "email_count": paste.get("EmailCount")
                            },
                            risk_score=6.0,  # Paste mentions are concerning
                            confidence=0.8,
                            found_at=datetime.now()
                        )
                        results.append(result)
                
                elif response.status == 404:
                    logger.info(f"No paste mentions found for {email}")
        
        except Exception as e:
            logger.error(f"Error checking pastes: {str(e)}")
        
        return results
    
    def _calculate_breach_risk(self, breach: Dict[str, Any]) -> float:
        """Calculate risk score for a breach."""
        base_score = 5.0
        
        # Higher score for sensitive breaches
        if breach.get("IsSensitive", False):
            base_score += 2.0
        
        # Higher score for recent breaches
        breach_date = breach.get("BreachDate")
        if breach_date:
            try:
                breach_year = int(breach_date.split("-")[0])
                current_year = datetime.now().year
                if current_year - breach_year <= 2:
                    base_score += 1.0
            except:
                pass
        
        # Higher score for large breaches
        pwn_count = breach.get("PwnCount", 0)
        if pwn_count > 10000000:  # 10M+
            base_score += 1.5
        elif pwn_count > 1000000:  # 1M+
            base_score += 1.0
        
        # Higher score for sensitive data types
        data_classes = breach.get("DataClasses", [])
        sensitive_classes = ["Passwords", "Credit cards", "Social security numbers", 
                           "Phone numbers", "Physical addresses"]
        
        for sensitive_class in sensitive_classes:
            if sensitive_class in data_classes:
                base_score += 0.5
        
        return min(base_score, 10.0)

class EmailRepCollector(OSINTCollector):
    """Collector for EmailRep reputation data."""
    
    def __init__(self):
        super().__init__("EmailRep", "reputation_database")
        self.api_key = os.getenv("EMAILREP_API_KEY")
        self.base_url = "https://emailrep.io"
        self.rate_limit = 300  # 300 requests per day for free tier
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Search for email reputation data."""
        results = []
        
        if not target.email:
            return results
        
        try:
            await self.rate_limit_check()
            
            headers = {}
            if self.api_key:
                headers["Key"] = self.api_key
            
            url = f"{self.base_url}/{target.email}"
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    result = OSINTResult(
                        source="EmailRep",
                        platform="reputation_database",
                        finding_type="reputation_check",
                        title=f"Email Reputation: {data.get('reputation', 'Unknown')}",
                        description=f"Reputation: {data.get('reputation')}. "
                                  f"Suspicious: {data.get('suspicious', False)}. "
                                  f"References: {data.get('references', 0)}",
                        url=f"https://emailrep.io/{target.email}",
                        metadata=data,
                        risk_score=self._calculate_reputation_risk(data),
                        confidence=0.8,
                        found_at=datetime.now()
                    )
                    results.append(result)
        
        except Exception as e:
            logger.error(f"Error checking EmailRep for {target.email}: {str(e)}")
        
        return results
    
    def _calculate_reputation_risk(self, data: Dict[str, Any]) -> float:
        """Calculate risk score based on reputation data."""
        base_score = 1.0
        
        if data.get("suspicious", False):
            base_score += 4.0
        
        reputation = data.get("reputation", "").lower()
        if reputation == "malicious":
            base_score += 6.0
        elif reputation == "suspicious":
            base_score += 3.0
        elif reputation == "low":
            base_score += 1.0
        
        # Check for spam/malware indicators
        details = data.get("details", {})
        if details.get("spam", False):
            base_score += 2.0
        if details.get("malware", False):
            base_score += 3.0
        if details.get("phishing", False):
            base_score += 4.0
        
        return min(base_score, 10.0)
