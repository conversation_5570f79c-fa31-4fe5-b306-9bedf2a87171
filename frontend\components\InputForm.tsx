import { useState } from "react";
import { useRouter } from "next/router";

interface Props {
  onSubmit: () => void;
}

export default function InputForm({ onSubmit }: Props) {
  const [form, setForm] = useState({
    name: "",
    username: "",
    email: "",
    phone: "",
    aliases: "",
    location: "",
    image: null as File | null
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, files } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData();
      if (form.name) formData.append("name", form.name);
      if (form.username) formData.append("username", form.username);
      if (form.email) formData.append("email", form.email);
      if (form.phone) formData.append("phone", form.phone);
      if (form.aliases) formData.append("aliases", form.aliases);
      if (form.location) formData.append("location", form.location);
      if (form.image) formData.append("image", form.image);

      const apiBase = process.env.NEXT_PUBLIC_API_BASE || "http://localhost:8000";

      const res = await fetch(`${apiBase}/api/scan`, {
        method: "POST",
        body: formData,
      });

      const data = await res.json();
      if (data.search_id) {
        localStorage.setItem("search_id", data.search_id);
        router.push("/result");
      } else {
        alert("Scan failed. Try again.");
      }
    } catch (error) {
      alert("Error starting scan. Please try again.");
      console.error("Scan error:", error);
    } finally {
      setIsLoading(false);
    }

    onSubmit();
  };

  return (
    <form onSubmit={handleSubmit} className="form">
      <input
        name="name"
        type="text"
        placeholder="Full Name"
        value={form.name}
        onChange={handleChange}
      />
      <input
        name="username"
        type="text"
        placeholder="Username"
        value={form.username}
        onChange={handleChange}
      />
      <input
        name="email"
        type="email"
        placeholder="Email Address"
        value={form.email}
        onChange={handleChange}
      />
      <input
        name="phone"
        type="tel"
        placeholder="Phone Number"
        value={form.phone}
        onChange={handleChange}
      />
      <input
        name="aliases"
        type="text"
        placeholder="Aliases (comma-separated)"
        value={form.aliases}
        onChange={handleChange}
      />
      <input
        name="location"
        type="text"
        placeholder="Location (optional)"
        value={form.location}
        onChange={handleChange}
      />
      <input
        name="image"
        type="file"
        accept="image/*"
        onChange={handleChange}
      />
      <button type="submit" disabled={isLoading}>
        {isLoading ? "Starting Scan..." : "Start OSINT Scan"}
      </button>
    </form>
  );
}
