import Head from "next/head";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import InputForm from "../components/InputForm";

export default function Home() {
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if user has accepted terms (only on client side)
    const checkTerms = () => {
      if (typeof window !== 'undefined') {
        try {
          const accepted = localStorage.getItem("terms_accepted");
          if (accepted === "true") {
            setTermsAccepted(true);
            setIsLoading(false);
          } else {
            // Redirect to terms page
            router.push("/terms");
          }
        } catch (error) {
          // If localStorage is not available, redirect to terms
          router.push("/terms");
        }
      }
    };

    checkTerms();
  }, [router]);

  // Show loading while checking terms
  if (isLoading || !termsAccepted) {
    return (
      <>
        <Head>
          <title>Ghostify — Loading</title>
        </Head>
        <div className="app-container">
          <div className="main-content">
            <div className="loading-section">
              <div className="loading-spinner"></div>
              <h2>Loading...</h2>
              <p>Checking terms acceptance...</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Ghostify — Professional OSINT Scanner</title>
        <meta name="description" content="Professional OSINT investigation platform for authorized digital footprint analysis" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className="app-container">
        <div className="main-content">
          <header className="hero-section">
            <div className="logo-container">
              <img src="/cyber-ducky-logo.svg" alt="CyberDucky Logo" className="main-logo" />
            </div>
            <h1 className="hero-title">Ghostify</h1>
            <p className="hero-subtitle">Professional OSINT Investigation Platform</p>
            <p className="hero-description">
              Analyze digital footprints through publicly available information sources
            </p>
          </header>

          <main className="content-section">
            {!submitted ? (
              <>
                <div className="info-cards">
                  <div className="info-card">
                    <div className="card-icon">🔍</div>
                    <h3>Comprehensive Analysis</h3>
                    <p>Multi-source intelligence gathering from public databases</p>
                  </div>
                  <div className="info-card">
                    <div className="card-icon">📊</div>
                    <h3>Professional Reports</h3>
                    <p>Detailed PDF reports with findings and risk assessment</p>
                  </div>
                  <div className="info-card">
                    <div className="card-icon">🛡️</div>
                    <h3>Privacy Focused</h3>
                    <p>No data storage - all processing is temporary and secure</p>
                  </div>
                </div>

                <div className="form-section">
                  <h2 className="form-title">Start Investigation</h2>
                  <p className="form-description">
                    Enter the information you want to investigate. Only investigate yourself or with explicit permission.
                  </p>
                  <InputForm onSubmit={() => setSubmitted(true)} />
                </div>
              </>
            ) : (
              <div className="loading-section">
                <div className="loading-spinner"></div>
                <h2>Investigation Started</h2>
                <p>Redirecting to results page...</p>
              </div>
            )}
          </main>

          <footer className="footer-section">
            <div className="legal-notice">
              <h4>⚖️ Legal Notice</h4>
              <p>
                This platform is for authorized investigations only. Users must comply with all applicable laws
                and have proper authorization. Only publicly available information is collected.
              </p>
            </div>
            <div className="footer-links">
              <button onClick={() => router.push("/terms")} className="footer-link">
                Terms & Legal Agreement
              </button>
            </div>
          </footer>
        </div>
      </div>
    </>
  );
}
