# 🦆 Ghostify - Advanced OSINT Investigation Platform

Ghostify is a comprehensive Open Source Intelligence (OSINT) web application that helps investigators gather, correlate, and report on digital footprints across multiple platforms and data sources.

## 🚀 Features

### Core OSINT Capabilities
- **Email Investigation**: Breach checking via HaveIBeenPwned, reputation analysis
- **Social Media Discovery**: Username enumeration across 10+ platforms (Sherlock-style)
- **Phone Number Analysis**: Validation, carrier lookup, spam detection
- **Search Engine Dorks**: Automated Google dorking for mentions and documents
- **Data Correlation**: Link identities across platforms using matching patterns
- **Risk Assessment**: Automated scoring and risk level determination

### Advanced Features
- **Multi-identifier Support**: Name, email, username, phone, aliases, location
- **Real-time Scanning**: Background processing with status updates
- **Professional Reports**: Comprehensive PDF reports with evidence and metadata
- **User Management**: Optional user accounts with search history
- **RESTful API**: Full API access for integration and automation

## 📋 Requirements

- Docker and Docker Compose
- 4GB+ RAM recommended
- Internet connection for OSINT data sources

## 🛠️ Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ghostify
```

### 2. Environment Setup
Create a `.env` file in the root directory:
```bash
# Database Configuration
DATABASE_URL=*****************************************************/ghostify

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production

# Optional API Keys (for enhanced functionality)
HIBP_API_KEY=your-haveibeenpwned-api-key
EMAILREP_API_KEY=your-emailrep-api-key
```

### 3. Start the Application
```bash
# Build and start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### 4. Access the Application
- **Frontend**: http://localhost:3001
- **API Documentation**: http://localhost:8000/docs
- **API Health Check**: http://localhost:8000/health

## 🔧 Configuration

### API Keys (Optional but Recommended)

#### HaveIBeenPwned API Key
1. Visit https://haveibeenpwned.com/API/Key
2. Purchase an API key ($3.50/month)
3. Add to `.env` file: `HIBP_API_KEY=your-key-here`

#### EmailRep API Key
1. Visit https://emailrep.io/
2. Sign up for free tier (300 requests/day)
3. Add to `.env` file: `EMAILREP_API_KEY=your-key-here`

### Database Configuration
The application uses PostgreSQL by default. For development, you can switch to SQLite:
```bash
DATABASE_URL=sqlite:///./ghostify.db
```

## 📖 Usage Guide

### Basic OSINT Scan
1. Navigate to http://localhost:3001
2. Enter target information:
   - **Name**: Full name of the target
   - **Username**: Known usernames or handles
   - **Email**: Email addresses to investigate
   - **Phone**: Phone numbers to analyze
   - **Aliases**: Comma-separated list of known aliases
   - **Location**: Geographic information (optional)
   - **Image**: Profile image for reverse image search (optional)
3. Click "Start OSINT Scan"
4. Monitor progress on the results page
5. Download the comprehensive PDF report when complete

### API Usage
```bash
# Start a scan
curl -X POST "http://localhost:8000/api/scan" \
  -H "Content-Type: multipart/form-data" \
  -F "email=<EMAIL>" \
  -F "username=targetuser" \
  -F "name=John Doe"

# Check scan status
curl "http://localhost:8000/api/scan/{search_id}/status"

# Get detailed results
curl "http://localhost:8000/api/scan/{search_id}/results"

# Download PDF report
curl "http://localhost:8000/api/report/{search_id}" -o report.pdf
```

### User Registration (Optional)
```bash
# Register a new user
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "investigator",
    "password": "secure_password",
    "full_name": "OSINT Investigator"
  }'

# Login
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "investigator",
    "password": "secure_password"
  }'
```

## 🏗️ Architecture

### Backend (FastAPI)
- **Framework**: FastAPI with async support
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT tokens with bcrypt password hashing
- **OSINT Modules**: Modular collector system
- **Report Generation**: WeasyPrint for PDF creation

### Frontend (Next.js)
- **Framework**: Next.js 14 with TypeScript
- **Styling**: Custom CSS with cyberpunk theme
- **State Management**: React hooks
- **API Integration**: Fetch API with error handling

### Database Schema
- **Users**: Account management and authentication
- **Searches**: Scan metadata and parameters
- **Findings**: Individual OSINT discoveries
- **Reports**: Generated PDF reports and sharing

## 🔍 OSINT Data Sources

### Currently Implemented
- **HaveIBeenPwned**: Email breach checking
- **EmailRep**: Email reputation analysis
- **Sherlock**: Username enumeration across platforms
- **Google Dorks**: Search engine intelligence
- **Phone Analysis**: Number validation and carrier lookup

### Planned Integrations
- **Shodan**: IoT and infrastructure discovery
- **VirusTotal**: File and URL analysis
- **Social Media APIs**: Direct platform integration
- **Dark Web Monitoring**: Tor-based searches
- **Public Records**: Government and business databases

## 🛡️ Security Considerations

### Data Handling
- All scans can be performed anonymously
- User data is encrypted at rest
- API keys are stored securely
- No sensitive data is logged

### Rate Limiting
- Built-in rate limiting for external APIs
- Respectful crawling practices
- Configurable delays between requests

### Legal Compliance
- Only uses publicly available information
- Respects robots.txt and terms of service
- Includes disclaimers in all reports

## 🐛 Troubleshooting

### Common Issues

#### 403 Forbidden Errors
- Ensure you're not sending authentication headers for anonymous scans
- Check that the API endpoints are accessible: `curl http://localhost:8000/health`

#### 404 Not Found Errors
- Verify all containers are running: `docker-compose ps`
- Check container logs: `docker-compose logs backend`
- Ensure database is initialized: `docker-compose logs database`
- Missing logo: The cyber-ducky-logo.svg should be in frontend/public/

#### Frontend Not Loading
- Check if frontend container is running on port 3001
- Verify environment variables are set correctly
- Clear browser cache and try again

#### JavaScript Errors (toFixed on null)
- This has been fixed with proper null checks in the frontend
- If you still see this error, restart the frontend: `docker-compose restart frontend`

#### Database Connection Issues
```bash
# Reset database
docker-compose down -v
docker-compose up -d

# Check database logs
docker-compose logs database
```

### Development Mode
```bash
# Run in development mode with hot reload
docker-compose -f docker-compose.dev.yml up

# Access backend directly
docker exec -it ghostify-backend bash

# View real-time logs
docker-compose logs -f backend frontend
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

Ghostify is intended for legitimate OSINT investigations, security research, and educational purposes only. Users are responsible for ensuring their use complies with applicable laws and regulations. The developers are not responsible for any misuse of this tool.

## 🆘 Support

- **Documentation**: Check this README and API docs at `/docs`
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Join community discussions
- **Email**: Contact the development team

---

**Happy Investigating! 🕵️‍♂️**
