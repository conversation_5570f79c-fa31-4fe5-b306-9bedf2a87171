"""
Sherlock Connector for Ghost<PERSON>
Wraps the Sherlock username search tool as a Ghostify connector
"""

import asyncio
import json
import os
import sys
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests

from .base import BaseConnector, ConnectorConfig, ConnectorResult, ConnectorEntity, EntityType, RiskLevel
from .entities import UsernameEntity, SocialMediaAccountEntity

# Add sherlock_lib to path
sherlock_lib_path = os.path.join(os.path.dirname(__file__), 'sherlock_lib')
if sherlock_lib_path not in sys.path:
    sys.path.insert(0, sherlock_lib_path)

# Try multiple import approaches
SitesInformation = None
try:
    from sherlock_lib.sites import SitesInformation
except ImportError:
    try:
        # Try direct import
        import sys
        import importlib.util
        sites_path = os.path.join(sherlock_lib_path, 'sites.py')
        if os.path.exists(sites_path):
            spec = importlib.util.spec_from_file_location("sites", sites_path)
            sites_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(sites_module)
            SitesInformation = sites_module.SitesInformation
    except Exception as e:
        print(f"Warning: Could not import Sherlock library: {e}")

# Fallback class if import fails
if SitesInformation is None:
    class SitesInformation:
        def __init__(self, data_file_path=None):
            self.data_file_path = data_file_path
            self.sites = {}

        def site_data_all(self):
            return self.sites


class SherlockConnector(BaseConnector):
    """
    Sherlock connector for username enumeration across social networks
    """
    
    def __init__(self, config: ConnectorConfig):
        super().__init__(config)
        self.sites_data = None
        self.session = None
        self._load_sites_data()
    
    @classmethod
    def get_config(cls) -> ConnectorConfig:
        return ConnectorConfig(
            name="sherlock",
            description="Hunt down social media accounts by username across 400+ social networks",
            version="0.15.0",
            author="Sherlock Project / Ghostify Integration",
            enabled=True,
            rate_limit=30,  # Conservative rate limit
            timeout=10,
            requires_api_key=False,
            supported_input_types=[EntityType.USERNAME, EntityType.PERSON],
            output_types=[EntityType.SOCIAL_MEDIA_ACCOUNT, EntityType.USERNAME],
            tags=["social_media", "username", "osint", "enumeration"]
        )
    
    def _load_sites_data(self):
        """Load Sherlock sites data"""
        try:
            data_file_path = os.path.join(sherlock_lib_path, 'resources', 'data.json')
            if os.path.exists(data_file_path):
                self.sites_data = SitesInformation(data_file_path)
                self.logger.info(f"Loaded {len(self.sites_data.site_data_all())} sites from Sherlock data")
            else:
                self.logger.warning("Sherlock data.json not found, using minimal site list")
                self.sites_data = self._create_minimal_sites_data()
        except Exception as e:
            self.logger.error(f"Error loading Sherlock sites data: {e}")
            self.sites_data = self._create_minimal_sites_data()
    
    def _create_minimal_sites_data(self):
        """Create minimal sites data for testing"""
        class MinimalSitesData:
            def site_data_all(self):
                return {
                    "GitHub": {
                        "url": "https://github.com/{}",
                        "urlMain": "https://github.com/",
                        "errorType": "status_code"
                    },
                    "Twitter": {
                        "url": "https://twitter.com/{}",
                        "urlMain": "https://twitter.com/",
                        "errorType": "status_code"
                    },
                    "Instagram": {
                        "url": "https://instagram.com/{}",
                        "urlMain": "https://instagram.com/",
                        "errorType": "status_code"
                    }
                }
        return MinimalSitesData()
    
    def can_process(self, entity: ConnectorEntity) -> bool:
        """Check if this connector can process the given entity"""
        if entity.entity_type == EntityType.USERNAME:
            return True
        elif entity.entity_type == EntityType.PERSON:
            # Can process person if they have a username property
            return 'username' in entity.properties or 'preferred_username' in entity.properties
        return False
    
    async def execute(self, entity: ConnectorEntity, **kwargs) -> ConnectorResult:
        """Execute Sherlock username search"""
        start_time = datetime.utcnow()
        
        # Extract username from entity
        if entity.entity_type == EntityType.USERNAME:
            username = entity.value
        elif entity.entity_type == EntityType.PERSON:
            username = entity.properties.get('username') or entity.properties.get('preferred_username')
            if not username:
                return self.create_result(entity, [], error="No username found in person entity")
        else:
            return self.create_result(entity, [], error="Unsupported entity type")
        
        # Validate username
        if not self._is_valid_username(username):
            return self.create_result(entity, [], error="Invalid username format")
        
        # Get site limit from kwargs
        site_limit = kwargs.get('site_limit', 50)  # Limit sites for performance
        timeout = kwargs.get('timeout', self.config.timeout)
        
        try:
            # Perform username search
            results = await self._search_username(username, site_limit, timeout)
            
            # Convert results to entities
            target_entities = []
            found_sites = []
            
            for result in results:
                if result['status'] == 'found':
                    # Create social media account entity
                    social_account = SocialMediaAccountEntity(
                        username=username,
                        platform=result['site_name'],
                        profile_url=result['url'],
                        response_time=result.get('response_time', 0),
                        confidence=result.get('confidence', 0.8)
                    )
                    target_entities.append(social_account)
                    found_sites.append(result['site_name'])
            
            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Determine risk level based on number of accounts found
            risk_level = RiskLevel.LOW
            if len(found_sites) > 10:
                risk_level = RiskLevel.MEDIUM
            if len(found_sites) > 25:
                risk_level = RiskLevel.HIGH
            
            # Create result
            result = ConnectorResult(
                source_entity=entity,
                target_entities=target_entities,
                metadata={
                    'found_sites': found_sites,
                    'total_sites_checked': len(results),
                    'found_count': len(found_sites),
                    'execution_time': execution_time,
                    'username_searched': username
                },
                risk_level=risk_level,
                confidence=0.9,
                execution_time=execution_time
            )
            
            self.logger.info(f"Sherlock found {len(found_sites)} accounts for username '{username}'")
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing Sherlock search: {e}")
            return self.create_result(
                entity, 
                [], 
                error=str(e),
                execution_time=(datetime.utcnow() - start_time).total_seconds()
            )
    
    def _is_valid_username(self, username: str) -> bool:
        """Validate username format"""
        if not username or len(username) < 1 or len(username) > 50:
            return False
        
        # Basic validation - alphanumeric, underscore, dash, dot
        import re
        return bool(re.match(r'^[a-zA-Z0-9._-]+$', username))
    
    async def _search_username(self, username: str, site_limit: int, timeout: int) -> List[Dict[str, Any]]:
        """Perform the actual username search"""
        if not self.sites_data:
            return []
        
        sites = self.sites_data.site_data_all()
        
        # Limit sites for performance
        limited_sites = dict(list(sites.items())[:site_limit])
        
        results = []
        
        # Use asyncio to make concurrent requests
        async with asyncio.Semaphore(10):  # Limit concurrent requests
            tasks = []
            for site_name, site_data in limited_sites.items():
                task = self._check_site(username, site_name, site_data, timeout)
                tasks.append(task)
            
            # Wait for all tasks to complete
            site_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in site_results:
                if isinstance(result, dict):
                    results.append(result)
                elif isinstance(result, Exception):
                    self.logger.warning(f"Site check failed: {result}")
        
        return results
    
    async def _check_site(self, username: str, site_name: str, site_data: Dict[str, Any], timeout: int) -> Dict[str, Any]:
        """Check a single site for the username"""
        try:
            # Build URL
            url = site_data.get('url', '').format(username)
            if not url:
                return {'site_name': site_name, 'status': 'error', 'error': 'No URL template'}
            
            # Make request
            start_time = datetime.utcnow()
            
            # Use asyncio to run the synchronous request in a thread
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: requests.get(url, timeout=timeout, allow_redirects=True)
            )
            
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Check if username exists based on error type
            error_type = site_data.get('errorType', 'status_code')
            status = 'not_found'
            
            if error_type == 'status_code':
                if response.status_code == 200:
                    status = 'found'
            elif error_type == 'message':
                error_msgs = site_data.get('errorMsg', [])
                if not any(msg in response.text for msg in error_msgs):
                    status = 'found'
            
            return {
                'site_name': site_name,
                'url': url,
                'status': status,
                'response_time': response_time,
                'status_code': response.status_code,
                'confidence': 0.8 if status == 'found' else 0.1
            }
            
        except Exception as e:
            return {
                'site_name': site_name,
                'status': 'error',
                'error': str(e),
                'confidence': 0.0
            }
