import { useState, useEffect } from "react";

interface Finding {
  id: string;
  source: string;
  platform: string;
  finding_type: string;
  title: string;
  description?: string;
  url?: string;
  risk_score?: number;
  confidence?: number;
  metadata?: any;
  found_at?: string;
}

interface ScanResultsProps {
  searchId: string;
}

export default function ScanResults({ searchId }: ScanResultsProps) {
  const [results, setResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFinding, setSelectedFinding] = useState<Finding | null>(null);

  useEffect(() => {
    fetchResults();
  }, [searchId]);

  const fetchResults = async () => {
    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE || "http://localhost:8000";
      const response = await fetch(`${apiBase}/api/scan/${searchId}/results`);
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error("Error fetching results:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRiskColor = (riskScore?: number) => {
    if (!riskScore) return "#6c757d";
    if (riskScore >= 7) return "#dc3545";
    if (riskScore >= 4) return "#fd7e14";
    if (riskScore >= 2) return "#ffc107";
    return "#28a745";
  };

  const groupFindingsByType = (findings: Finding[]) => {
    const groups: { [key: string]: Finding[] } = {};
    findings.forEach(finding => {
      const type = finding.finding_type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(finding);
    });
    return groups;
  };

  if (isLoading) {
    return <div className="loading">Loading results...</div>;
  }

  if (!results || !results.findings || results.findings.length === 0) {
    return (
      <div className="no-results">
        <h3>No Findings</h3>
        <p>No information was found during this scan.</p>
      </div>
    );
  }

  const groupedFindings = groupFindingsByType(results.findings);

  return (
    <div className="scan-results">
      <h2>Detailed Findings ({results.findings.length})</h2>
      
      {Object.entries(groupedFindings).map(([type, findings]) => (
        <div key={type} className="finding-group">
          <h3 className="group-title">
            {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} ({findings.length})
          </h3>
          
          <div className="findings-list">
            {findings.map((finding: Finding) => (
              <div 
                key={finding.id} 
                className="finding-card"
                onClick={() => setSelectedFinding(finding)}
              >
                <div className="finding-header">
                  <h4 className="finding-title">{finding.title}</h4>
                  <div className="finding-meta">
                    <span className="source-badge">{finding.source}</span>
                    <span className="platform-badge">{finding.platform}</span>
                    {finding.risk_score && (
                      <span 
                        className="risk-badge"
                        style={{ 
                          backgroundColor: getRiskColor(finding.risk_score),
                          color: "white"
                        }}
                      >
                        Risk: {finding.risk_score.toFixed(1)}
                      </span>
                    )}
                    {finding.confidence && (
                      <span className="confidence-badge">
                        {Math.round(finding.confidence * 100)}% confidence
                      </span>
                    )}
                  </div>
                </div>
                
                {finding.description && (
                  <p className="finding-description">{finding.description}</p>
                )}
                
                {finding.url && (
                  <a 
                    href={finding.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="finding-url"
                    onClick={(e) => e.stopPropagation()}
                  >
                    View Source →
                  </a>
                )}
                
                {finding.found_at && (
                  <div className="finding-timestamp">
                    Found: {new Date(finding.found_at).toLocaleString()}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Modal for detailed finding view */}
      {selectedFinding && (
        <div className="modal-overlay" onClick={() => setSelectedFinding(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{selectedFinding.title}</h3>
              <button 
                className="close-button"
                onClick={() => setSelectedFinding(null)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="finding-details">
                <p><strong>Source:</strong> {selectedFinding.source}</p>
                <p><strong>Platform:</strong> {selectedFinding.platform}</p>
                <p><strong>Type:</strong> {selectedFinding.finding_type}</p>
                
                {selectedFinding.risk_score && (
                  <p>
                    <strong>Risk Score:</strong> 
                    <span style={{ color: getRiskColor(selectedFinding.risk_score) }}>
                      {selectedFinding.risk_score.toFixed(1)}/10
                    </span>
                  </p>
                )}
                
                {selectedFinding.confidence && (
                  <p><strong>Confidence:</strong> {Math.round(selectedFinding.confidence * 100)}%</p>
                )}
                
                {selectedFinding.description && (
                  <div>
                    <strong>Description:</strong>
                    <p>{selectedFinding.description}</p>
                  </div>
                )}
                
                {selectedFinding.url && (
                  <p>
                    <strong>URL:</strong> 
                    <a 
                      href={selectedFinding.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      {selectedFinding.url}
                    </a>
                  </p>
                )}
                
                {selectedFinding.metadata && (
                  <details>
                    <summary><strong>Additional Details</strong></summary>
                    <pre className="metadata-display">
                      {JSON.stringify(selectedFinding.metadata, null, 2)}
                    </pre>
                  </details>
                )}
                
                {selectedFinding.found_at && (
                  <p>
                    <strong>Found At:</strong> {new Date(selectedFinding.found_at).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
