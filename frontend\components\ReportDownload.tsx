interface ReportDownloadProps {
  searchId: string;
}

export default function ReportDownload({ searchId }: ReportDownloadProps) {
  const handleDownload = async () => {
    if (!searchId) {
      alert("No search ID found.");
      return;
    }

    try {
      const apiBase = process.env.NEXT_PUBLIC_API_BASE || "http://localhost:8000";
      const res = await fetch(`${apiBase}/api/report/${searchId}`);

      if (!res.ok) {
        alert("Failed to download report. Please try again.");
        return;
      }

      const blob = await res.blob();
      const a = document.createElement("a");
      a.href = window.URL.createObjectURL(blob);
      a.download = `ghostify-report-${searchId}.pdf`;
      a.click();
    } catch (error) {
      console.error("Download error:", error);
      alert("Error downloading report. Please try again.");
    }
  };

  return (
    <div className="report-download">
      <h3>Download Report</h3>
      <p>Get a comprehensive PDF report of all findings.</p>
      <button onClick={handleDownload} className="download-button">
        📄 Download PDF Report
      </button>
    </div>
  );
}
