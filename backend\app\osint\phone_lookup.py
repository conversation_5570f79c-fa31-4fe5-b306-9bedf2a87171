import aiohttp
import logging
from typing import List, Dict, Any
from datetime import datetime
from app.osint.base import OSINTCollector, OSINTResult, SearchTarget
import phonenumbers
from phonenumbers import geocoder, carrier
import re

logger = logging.getLogger(__name__)

class PhoneNumberCollector(OSINTCollector):
    """Phone number lookup and validation."""
    
    def __init__(self):
        super().__init__("PhoneNumber", "phone_lookup")
        self.rate_limit = 60
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Search for phone number information."""
        results = []
        
        if not target.phone:
            return results
        
        try:
            # Parse and validate phone number
            phone_info = self._parse_phone_number(target.phone)
            if phone_info:
                results.append(phone_info)
            
            # Check for spam reports
            spam_info = await self._check_spam_reports(target.phone)
            if spam_info:
                results.append(spam_info)
            
            # Search for phone number mentions
            mention_results = await self._search_phone_mentions(target.phone)
            results.extend(mention_results)
        
        except Exception as e:
            logger.error(f"Error processing phone number {target.phone}: {str(e)}")
        
        return results
    
    def _parse_phone_number(self, phone: str) -> OSINTResult:
        """Parse and extract information from phone number."""
        try:
            # Clean phone number
            cleaned_phone = re.sub(r'[^\d+]', '', phone)
            
            # Parse with phonenumbers library (default to US region)
            parsed = phonenumbers.parse(cleaned_phone, "US")
            
            if phonenumbers.is_valid_number(parsed):
                # Get location and carrier info
                location = geocoder.description_for_number(parsed, "en")
                carrier_name = carrier.name_for_number(parsed, "en")
                
                # Get number type safely
                number_type = phonenumbers.number_type(parsed)
                number_type_name = getattr(number_type, 'name', str(number_type))

                metadata = {
                    "formatted_number": phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL),
                    "country_code": parsed.country_code,
                    "national_number": parsed.national_number,
                    "location": location,
                    "carrier": carrier_name,
                    "number_type": number_type_name,
                    "is_valid": True
                }
                
                return OSINTResult(
                    source="PhoneNumber",
                    platform="phone_validation",
                    finding_type="phone_info",
                    title="Phone Number Information",
                    description=f"Valid phone number from {location}. Carrier: {carrier_name}",
                    metadata=metadata,
                    risk_score=1.0,  # Basic info is low risk
                    confidence=0.9,
                    found_at=datetime.now()
                )
            else:
                return OSINTResult(
                    source="PhoneNumber",
                    platform="phone_validation",
                    finding_type="phone_info",
                    title="Invalid Phone Number",
                    description="Phone number appears to be invalid",
                    metadata={"is_valid": False, "original_input": phone},
                    risk_score=0.5,
                    confidence=0.8,
                    found_at=datetime.now()
                )
        
        except Exception as e:
            logger.error(f"Error parsing phone number: {str(e)}")
            return None
    
    async def _check_spam_reports(self, phone: str) -> OSINTResult:
        """Check for spam reports (mock implementation)."""
        # In a real implementation, you'd integrate with services like:
        # - TrueCaller API
        # - Whocalld.com
        # - SpamCalls.net
        
        # Mock spam check
        spam_indicators = ["spam", "scam", "telemarketer", "robocall"]
        
        # Simulate spam check result
        is_spam = hash(phone) % 10 == 0  # 10% chance of being flagged as spam
        
        if is_spam:
            return OSINTResult(
                source="PhoneNumber",
                platform="spam_database",
                finding_type="spam_report",
                title="Spam Reports Found",
                description="This phone number has been reported as spam/scam",
                metadata={
                    "spam_reports": 5,
                    "report_types": ["telemarketer", "spam"],
                    "last_reported": "2024-01-15"
                },
                risk_score=6.0,
                confidence=0.7,
                found_at=datetime.now()
            )
        
        return None
    
    async def _search_phone_mentions(self, phone: str) -> List[OSINTResult]:
        """Search for phone number mentions online."""
        results = []
        
        # Format phone number in different ways for searching
        phone_formats = self._generate_phone_formats(phone)
        
        for phone_format in phone_formats:
            try:
                await self.rate_limit_check()
                
                # Mock search implementation
                # In real implementation, search across:
                # - Social media platforms
                # - Business directories
                # - Public records
                # - Classified ads
                
                # Simulate finding mentions
                if hash(phone_format) % 5 == 0:  # 20% chance of finding mentions
                    result = OSINTResult(
                        source="PhoneNumber",
                        platform="web_search",
                        finding_type="phone_mention",
                        title="Phone Number Mention Found",
                        description=f"Phone number {phone_format} found in online content",
                        url=f"https://example.com/listing/{phone_format}",
                        metadata={
                            "phone_format": phone_format,
                            "context": "business_listing",
                            "source_type": "directory"
                        },
                        risk_score=2.0,
                        confidence=0.6,
                        found_at=datetime.now()
                    )
                    results.append(result)
            
            except Exception as e:
                logger.error(f"Error searching for phone mentions: {str(e)}")
        
        return results
    
    def _generate_phone_formats(self, phone: str) -> List[str]:
        """Generate different formats of the phone number for searching."""
        formats = []
        
        try:
            # Clean phone number
            cleaned = re.sub(r'[^\d+]', '', phone)
            
            # Parse phone number (default to US region)
            parsed = phonenumbers.parse(cleaned, "US")
            
            if phonenumbers.is_valid_number(parsed):
                # Add different formats
                formats.append(phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164))
                formats.append(phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL))
                formats.append(phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.NATIONAL))
                
                # Add common formatting variations
                national = str(parsed.national_number)
                if len(national) == 10:  # US number
                    formats.extend([
                        f"({national[:3]}) {national[3:6]}-{national[6:]}",
                        f"{national[:3]}-{national[3:6]}-{national[6:]}",
                        f"{national[:3]}.{national[3:6]}.{national[6:]}",
                        national
                    ])
        
        except Exception as e:
            logger.error(f"Error generating phone formats: {str(e)}")
            # Fallback to original input
            formats.append(phone)
        
        return list(set(formats))  # Remove duplicates

class ReversePhoneLookupCollector(OSINTCollector):
    """Reverse phone lookup using public directories."""
    
    def __init__(self):
        super().__init__("ReversePhoneLookup", "phone_lookup")
        self.rate_limit = 30
    
    async def search(self, target: SearchTarget) -> List[OSINTResult]:
        """Perform reverse phone lookup."""
        results = []
        
        if not target.phone:
            return results
        
        try:
            # Mock reverse lookup
            # In real implementation, integrate with:
            # - WhitePages API
            # - TruePeopleSearch
            # - FastPeopleSearch
            # - BeenVerified API
            
            lookup_result = await self._mock_reverse_lookup(target.phone)
            if lookup_result:
                results.append(lookup_result)
        
        except Exception as e:
            logger.error(f"Error in reverse phone lookup: {str(e)}")
        
        return results
    
    async def _mock_reverse_lookup(self, phone: str) -> OSINTResult:
        """Mock reverse phone lookup."""
        await self.rate_limit_check()
        
        # Simulate finding owner information
        if hash(phone) % 3 == 0:  # 33% chance of finding owner info
            return OSINTResult(
                source="ReversePhoneLookup",
                platform="public_directory",
                finding_type="owner_info",
                title="Phone Owner Information",
                description="Owner information found in public directory",
                metadata={
                    "owner_name": "John D. Smith",
                    "location": "Seattle, WA",
                    "carrier": "Verizon Wireless",
                    "line_type": "Mobile",
                    "associated_addresses": [
                        "123 Main St, Seattle, WA 98101"
                    ]
                },
                risk_score=3.0,  # Personal info exposure
                confidence=0.7,
                found_at=datetime.now()
            )
        
        return None
