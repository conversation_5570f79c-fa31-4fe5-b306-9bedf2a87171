# Ghostify Connector Framework
# Inspired by Maltego's Transform architecture for extensible OSINT data collection

from .base import (
    BaseConnector,
    ConnectorR<PERSON>ult,
    ConnectorEntity,
    ConnectorConfig,
    ConnectorError,
    ConnectorRegistry
)

from .entities import (
    PersonEntity,
    EmailEntity,
    UsernameEntity,
    PhoneEntity,
    DomainEntity,
    IPAddressEntity,
    LocationEntity,
    SocialMediaAccountEntity,
    DocumentEntity,
    ImageEntity
)

from .sherlock_connector import SherlockConnector

__all__ = [
    'BaseConnector',
    'ConnectorResult',
    'ConnectorEntity',
    'ConnectorConfig',
    'ConnectorError',
    'ConnectorRegistry',
    'PersonEntity',
    'EmailEntity',
    'UsernameEntity',
    'PhoneEntity',
    'DomainEntity',
    'IPAddressEntity',
    'LocationEntity',
    'SocialMediaAccountEntity',
    'DocumentEntity',
    'ImageEntity',
    'SherlockConnector'
]
