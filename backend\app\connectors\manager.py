"""
Connector Manager for Ghostify
Manages and orchestrates all OSINT connectors
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from .base import (
    BaseConnector, 
    ConnectorResult, 
    ConnectorEntity, 
    EntityType, 
    ConnectorRegistry,
    registry
)
from .entities import PersonEntity, EmailEntity, UsernameEntity
from .sherlock_connector import SherlockConnector

logger = logging.getLogger(__name__)


class ConnectorManager:
    """
    Manages and orchestrates OSINT connectors
    Inspired by Maltego's transform execution engine
    """
    
    def __init__(self):
        self.registry = registry
        self._initialize_connectors()
    
    def _initialize_connectors(self):
        """Initialize and register all available connectors"""
        try:
            # Register Sherlock connector
            self.registry.register(SherlockConnector)
            logger.info("Initialized connector registry")
        except Exception as e:
            logger.error(f"Error initializing connectors: {e}")
    
    async def execute_scan(self, scan_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a complete OSINT scan using available connectors
        
        Args:
            scan_data: Dictionary containing scan parameters
            
        Returns:
            Dictionary containing scan results
        """
        start_time = datetime.utcnow()
        
        # Extract entities from scan data
        entities = self._extract_entities_from_scan_data(scan_data)
        
        if not entities:
            return {
                'status': 'error',
                'message': 'No valid entities found in scan data',
                'results': []
            }
        
        # Execute connectors for each entity
        all_results = []
        connector_stats = {}
        
        for entity in entities:
            entity_results = await self._execute_connectors_for_entity(entity)
            all_results.extend(entity_results)
            
            # Update stats
            for result in entity_results:
                connector_name = result.metadata.get('connector_name', 'unknown')
                if connector_name not in connector_stats:
                    connector_stats[connector_name] = {'executed': 0, 'found': 0}
                connector_stats[connector_name]['executed'] += 1
                if result.entity_count > 0:
                    connector_stats[connector_name]['found'] += 1
        
        # Calculate execution time
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Aggregate results
        aggregated_results = self._aggregate_results(all_results)
        
        return {
            'status': 'completed',
            'execution_time': execution_time,
            'entities_processed': len(entities),
            'connectors_executed': len(connector_stats),
            'total_findings': sum(r.entity_count for r in all_results),
            'connector_stats': connector_stats,
            'results': aggregated_results,
            'raw_results': [self._serialize_result(r) for r in all_results]
        }
    
    def _extract_entities_from_scan_data(self, scan_data: Dict[str, Any]) -> List[ConnectorEntity]:
        """Extract entities from scan request data"""
        entities = []
        
        # Extract person entity
        if scan_data.get('name'):
            person = PersonEntity(
                name=scan_data['name'],
                email=scan_data.get('email'),
                phone=scan_data.get('phone'),
                location=scan_data.get('location'),
                aliases=scan_data.get('aliases')
            )
            entities.append(person)
        
        # Extract email entity
        if scan_data.get('email'):
            email = EmailEntity(scan_data['email'])
            entities.append(email)
        
        # Extract username entity
        if scan_data.get('username'):
            username = UsernameEntity(scan_data['username'])
            entities.append(username)
        
        # Extract usernames from aliases
        if scan_data.get('aliases'):
            aliases = scan_data['aliases'].split(',')
            for alias in aliases:
                alias = alias.strip()
                if alias:
                    username = UsernameEntity(alias)
                    entities.append(username)
        
        return entities
    
    async def _execute_connectors_for_entity(self, entity: ConnectorEntity) -> List[ConnectorResult]:
        """Execute all applicable connectors for a given entity"""
        applicable_connectors = self.registry.get_connectors_for_entity(entity.entity_type)
        
        if not applicable_connectors:
            logger.warning(f"No connectors available for entity type: {entity.entity_type}")
            return []
        
        # Execute connectors concurrently
        tasks = []
        for connector in applicable_connectors:
            if connector.config.enabled:
                task = self._execute_connector_safely(connector, entity)
                tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and add connector metadata
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, ConnectorResult):
                # Add connector metadata
                result.metadata['connector_name'] = applicable_connectors[i].config.name
                result.metadata['connector_version'] = applicable_connectors[i].config.version
                valid_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Connector execution failed: {result}")
        
        return valid_results
    
    async def _execute_connector_safely(self, connector: BaseConnector, entity: ConnectorEntity) -> ConnectorResult:
        """Execute a connector with error handling and rate limiting"""
        try:
            # Validate input
            if not await connector.validate_input(entity):
                return ConnectorResult(
                    source_entity=entity,
                    target_entities=[],
                    metadata={'error': 'Input validation failed'}
                )
            
            # Apply rate limiting
            await connector.rate_limit_check()
            
            # Execute connector
            result = await connector.execute(entity)
            
            logger.info(f"Connector {connector.config.name} found {result.entity_count} entities")
            return result
            
        except Exception as e:
            logger.error(f"Error executing connector {connector.config.name}: {e}")
            return ConnectorResult(
                source_entity=entity,
                target_entities=[],
                metadata={'error': str(e), 'connector_name': connector.config.name}
            )
    
    def _aggregate_results(self, results: List[ConnectorResult]) -> Dict[str, Any]:
        """Aggregate results by entity type and source"""
        aggregated = {
            'social_media_accounts': [],
            'emails': [],
            'usernames': [],
            'domains': [],
            'ip_addresses': [],
            'locations': [],
            'documents': [],
            'images': [],
            'summary': {
                'total_entities': 0,
                'by_type': {},
                'by_connector': {},
                'risk_levels': {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
            }
        }
        
        for result in results:
            connector_name = result.metadata.get('connector_name', 'unknown')
            
            # Update connector stats
            if connector_name not in aggregated['summary']['by_connector']:
                aggregated['summary']['by_connector'][connector_name] = 0
            aggregated['summary']['by_connector'][connector_name] += result.entity_count
            
            # Update risk level stats
            risk_level = result.risk_level.value
            aggregated['summary']['risk_levels'][risk_level] += 1
            
            # Categorize entities
            for entity in result.target_entities:
                entity_data = {
                    'value': entity.value,
                    'display_name': entity.display_name,
                    'properties': entity.properties,
                    'confidence': entity.confidence,
                    'source': entity.source,
                    'connector': connector_name,
                    'found_at': result.found_at.isoformat()
                }
                
                entity_type = entity.entity_type.value
                
                # Update type stats
                if entity_type not in aggregated['summary']['by_type']:
                    aggregated['summary']['by_type'][entity_type] = 0
                aggregated['summary']['by_type'][entity_type] += 1
                
                # Add to appropriate category
                if entity_type == 'social_media_account':
                    aggregated['social_media_accounts'].append(entity_data)
                elif entity_type == 'email':
                    aggregated['emails'].append(entity_data)
                elif entity_type == 'username':
                    aggregated['usernames'].append(entity_data)
                elif entity_type == 'domain':
                    aggregated['domains'].append(entity_data)
                elif entity_type == 'ip_address':
                    aggregated['ip_addresses'].append(entity_data)
                elif entity_type == 'location':
                    aggregated['locations'].append(entity_data)
                elif entity_type == 'document':
                    aggregated['documents'].append(entity_data)
                elif entity_type == 'image':
                    aggregated['images'].append(entity_data)
                
                aggregated['summary']['total_entities'] += 1
        
        return aggregated
    
    def _serialize_result(self, result: ConnectorResult) -> Dict[str, Any]:
        """Serialize a ConnectorResult for JSON output"""
        return {
            'source_entity': {
                'type': result.source_entity.entity_type.value,
                'value': result.source_entity.value,
                'display_name': result.source_entity.display_name
            },
            'target_entities': [
                {
                    'type': entity.entity_type.value,
                    'value': entity.value,
                    'display_name': entity.display_name,
                    'properties': entity.properties,
                    'confidence': entity.confidence,
                    'source': entity.source
                }
                for entity in result.target_entities
            ],
            'metadata': result.metadata,
            'risk_level': result.risk_level.value,
            'confidence': result.confidence,
            'execution_time': result.execution_time,
            'found_at': result.found_at.isoformat(),
            'entity_count': result.entity_count
        }
    
    def get_available_connectors(self) -> List[Dict[str, Any]]:
        """Get list of available connectors"""
        configs = self.registry.list_connectors()
        return [
            {
                'name': config.name,
                'description': config.description,
                'version': config.version,
                'author': config.author,
                'enabled': config.enabled,
                'requires_api_key': config.requires_api_key,
                'supported_input_types': [t.value for t in config.supported_input_types],
                'output_types': [t.value for t in config.output_types],
                'tags': config.tags
            }
            for config in configs
        ]


# Global connector manager instance
connector_manager = ConnectorManager()
