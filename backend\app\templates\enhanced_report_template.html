<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Ghostify OSINT Report</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background: #ffffff;
      color: #333333;
      padding: 2rem;
      line-height: 1.6;
    }
    
    .header {
      text-align: center;
      border-bottom: 3px solid #00ffe7;
      padding-bottom: 1rem;
      margin-bottom: 2rem;
    }
    
    .header h1 {
      color: #0f0f1e;
      font-size: 2.5rem;
      margin: 0;
    }
    
    .header .subtitle {
      color: #666;
      font-size: 1.2rem;
      margin: 0.5rem 0;
    }
    
    .summary-section {
      background: #f8f9fa;
      border-left: 4px solid #00ffe7;
      padding: 1.5rem;
      margin: 2rem 0;
    }
    
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }
    
    .summary-item {
      text-align: center;
      padding: 1rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .summary-item .value {
      font-size: 2rem;
      font-weight: bold;
      color: #00ffe7;
    }
    
    .summary-item .label {
      color: #666;
      font-size: 0.9rem;
    }
    
    .risk-level {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: bold;
      text-transform: uppercase;
    }
    
    .risk-low { background: #d4edda; color: #155724; }
    .risk-medium { background: #fff3cd; color: #856404; }
    .risk-high { background: #f8d7da; color: #721c24; }
    .risk-critical { background: #f5c6cb; color: #721c24; }
    
    .target-info {
      background: #e9ecef;
      padding: 1.5rem;
      border-radius: 8px;
      margin: 2rem 0;
    }
    
    .target-info h2 {
      margin-top: 0;
      color: #0f0f1e;
    }
    
    .target-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }
    
    .findings-section {
      margin: 2rem 0;
    }
    
    .finding {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      margin: 1rem 0;
      overflow: hidden;
    }
    
    .finding-header {
      background: #f8f9fa;
      padding: 1rem;
      border-bottom: 1px solid #dee2e6;
    }
    
    .finding-title {
      margin: 0;
      color: #0f0f1e;
      font-size: 1.2rem;
    }
    
    .finding-meta {
      color: #666;
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }
    
    .finding-body {
      padding: 1rem;
    }
    
    .risk-score {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: bold;
    }
    
    .risk-score-low { background: #d4edda; color: #155724; }
    .risk-score-medium { background: #fff3cd; color: #856404; }
    .risk-score-high { background: #f8d7da; color: #721c24; }
    
    .confidence {
      color: #666;
      font-size: 0.9rem;
    }
    
    .finding-url {
      color: #007bff;
      text-decoration: none;
      word-break: break-all;
    }
    
    .footer {
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid #dee2e6;
      text-align: center;
      color: #666;
      font-size: 0.9rem;
    }
    
    .page-break {
      page-break-before: always;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🦆 Ghostify OSINT Report</h1>
    <div class="subtitle">Open Source Intelligence Investigation</div>
    <div class="subtitle">Generated: {{ generated_at.strftime('%Y-%m-%d %H:%M:%S UTC') }}</div>
  </div>

  <!-- Summary Section -->
  <div class="summary-section">
    <h2>Executive Summary</h2>
    <div class="summary-grid">
      <div class="summary-item">
        <div class="value">{{ summary.total_findings }}</div>
        <div class="label">Total Findings</div>
      </div>
      <div class="summary-item">
        <div class="value">{{ "%.1f"|format(summary.exposure_score) }}</div>
        <div class="label">Exposure Score</div>
      </div>
      <div class="summary-item">
        <div class="value">{{ summary.breach_count }}</div>
        <div class="label">Data Breaches</div>
      </div>
      <div class="summary-item">
        <div class="value">{{ summary.social_profiles }}</div>
        <div class="label">Social Profiles</div>
      </div>
    </div>
    
    <div style="margin-top: 1rem;">
      <strong>Risk Level:</strong> 
      <span class="risk-level risk-{{ summary.risk_level }}">{{ summary.risk_level }}</span>
    </div>
    
    {% if summary.dark_web_mentions %}
    <div style="margin-top: 1rem; color: #721c24;">
      <strong>⚠️ Dark Web Mentions Detected</strong>
    </div>
    {% endif %}
  </div>

  <!-- Target Information -->
  <div class="target-info">
    <h2>Target Information</h2>
    <div class="target-grid">
      {% if search.target_name %}
      <div><strong>Name:</strong> {{ search.target_name }}</div>
      {% endif %}
      {% if search.target_email %}
      <div><strong>Email:</strong> {{ search.target_email }}</div>
      {% endif %}
      {% if search.target_username %}
      <div><strong>Username:</strong> {{ search.target_username }}</div>
      {% endif %}
      {% if search.target_phone %}
      <div><strong>Phone:</strong> {{ search.target_phone }}</div>
      {% endif %}
      {% if search.target_location %}
      <div><strong>Location:</strong> {{ search.target_location }}</div>
      {% endif %}
      {% if search.target_aliases %}
      <div><strong>Aliases:</strong> {{ search.target_aliases|join(', ') }}</div>
      {% endif %}
    </div>
    
    <div style="margin-top: 1rem;">
      <strong>Scan Started:</strong> {{ search.created_at.strftime('%Y-%m-%d %H:%M:%S UTC') }}<br>
      <strong>Scan Completed:</strong> {{ search.completed_at.strftime('%Y-%m-%d %H:%M:%S UTC') if search.completed_at else 'In Progress' }}
    </div>
  </div>

  <!-- Findings Section -->
  <div class="findings-section">
    <h2>Detailed Findings</h2>
    
    {% if findings %}
      {% for finding in findings %}
      <div class="finding">
        <div class="finding-header">
          <h3 class="finding-title">{{ finding.title or 'Finding' }}</h3>
          <div class="finding-meta">
            <strong>Source:</strong> {{ finding.source }} | 
            <strong>Platform:</strong> {{ finding.platform }} | 
            <strong>Type:</strong> {{ finding.finding_type }}
            {% if finding.risk_score %}
            | <span class="risk-score risk-score-{% if finding.risk_score >= 7 %}high{% elif finding.risk_score >= 4 %}medium{% else %}low{% endif %}">
              Risk: {{ "%.1f"|format(finding.risk_score) }}/10
            </span>
            {% endif %}
            {% if finding.confidence %}
            | <span class="confidence">Confidence: {{ "%.0f"|format(finding.confidence * 100) }}%</span>
            {% endif %}
          </div>
        </div>
        
        <div class="finding-body">
          {% if finding.description %}
          <p>{{ finding.description }}</p>
          {% endif %}
          
          {% if finding.url %}
          <p><strong>URL:</strong> <a href="{{ finding.url }}" class="finding-url">{{ finding.url }}</a></p>
          {% endif %}
          
          {% if finding.finding_metadata %}
          <details>
            <summary><strong>Additional Details</strong></summary>
            <pre style="background: #f8f9fa; padding: 1rem; border-radius: 4px; font-size: 0.9rem;">{{ finding.finding_metadata|safe_json }}</pre>
          </details>
          {% endif %}
          
          {% if finding.found_at %}
          <p style="color: #666; font-size: 0.9rem; margin-top: 1rem;">
            <strong>Found:</strong> {{ finding.found_at.strftime('%Y-%m-%d %H:%M:%S UTC') }}
          </p>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    {% else %}
      <p>No findings were discovered during this investigation.</p>
    {% endif %}
  </div>

  <div class="footer">
    <p>This report was generated by Ghostify OSINT Platform</p>
    <p>Report ID: {{ search.id }}</p>
    <p><strong>Disclaimer:</strong> This report is for informational purposes only. All data is collected from publicly available sources.</p>
  </div>
</body>
</html>
