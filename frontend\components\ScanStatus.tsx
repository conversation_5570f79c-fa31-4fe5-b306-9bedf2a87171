interface ScanStatusProps {
  status: {
    search_id: string;
    status: string;
    exposure_score?: number;
    risk_level?: string;
    findings_count: number;
    created_at: string;
    completed_at?: string;
  };
}

export default function ScanStatus({ status }: ScanStatusProps) {
  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case "completed":
        return "#28a745";
      case "running":
        return "#ffc107";
      case "pending":
        return "#6c757d";
      case "failed":
        return "#dc3545";
      default:
        return "#6c757d";
    }
  };

  const getRiskColor = (riskLevel?: string) => {
    switch (riskLevel) {
      case "low":
        return "#28a745";
      case "medium":
        return "#ffc107";
      case "high":
        return "#fd7e14";
      case "critical":
        return "#dc3545";
      default:
        return "#6c757d";
    }
  };

  return (
    <div className="scan-status">
      <h2>Scan Status</h2>
      
      <div className="status-grid">
        <div className="status-item">
          <div className="status-label">Status</div>
          <div 
            className="status-value"
            style={{ color: getStatusColor(status.status) }}
          >
            {status.status.toUpperCase()}
            {status.status === "running" && (
              <span className="loading-spinner"> ⟳</span>
            )}
          </div>
        </div>

        <div className="status-item">
          <div className="status-label">Findings</div>
          <div className="status-value">{status.findings_count}</div>
        </div>

        {status.exposure_score !== undefined && status.exposure_score !== null && (
          <div className="status-item">
            <div className="status-label">Exposure Score</div>
            <div className="status-value">
              {status.exposure_score.toFixed(1)}/10
            </div>
          </div>
        )}

        {status.risk_level && (
          <div className="status-item">
            <div className="status-label">Risk Level</div>
            <div 
              className="status-value risk-badge"
              style={{ 
                backgroundColor: getRiskColor(status.risk_level),
                color: "white",
                padding: "0.25rem 0.5rem",
                borderRadius: "4px",
                fontSize: "0.9rem"
              }}
            >
              {status.risk_level.toUpperCase()}
            </div>
          </div>
        )}
      </div>

      <div className="scan-timeline">
        <div className="timeline-item">
          <strong>Started:</strong> {new Date(status.created_at).toLocaleString()}
        </div>
        {status.completed_at && (
          <div className="timeline-item">
            <strong>Completed:</strong> {new Date(status.completed_at).toLocaleString()}
          </div>
        )}
      </div>

      {status.status === "running" && (
        <div className="progress-message">
          <p>🔍 Scanning in progress... This may take a few minutes.</p>
          <p>We're searching across multiple data sources and platforms.</p>
        </div>
      )}

      {status.status === "pending" && (
        <div className="progress-message">
          <p>⏳ Scan queued and will start shortly...</p>
        </div>
      )}
    </div>
  );
}
