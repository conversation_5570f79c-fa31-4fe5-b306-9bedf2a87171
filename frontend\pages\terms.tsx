import Head from "next/head";
import { useState } from "react";
import { useRouter } from "next/router";

export default function Terms() {
  const [agreed, setAgreed] = useState(false);
  const router = useRouter();

  const handleAccept = () => {
    if (agreed) {
      if (typeof window !== 'undefined') {
        localStorage.setItem("terms_accepted", "true");
        router.push("/");
      }
    }
  };

  const handleDecline = () => {
    // Redirect to external site
    if (typeof window !== 'undefined') {
      window.location.href = "https://www.google.com";
    }
  };

  return (
    <>
      <Head>
        <title>Ghostify - Terms & Legal Agreement</title>
        <meta name="description" content="Legal terms and conditions for using Ghostify OSINT platform" />
      </Head>
      
      <div className="app-container">
        <div className="main-content">
          <div className="terms-container">
          <header className="terms-header">
            <img src="/cyber-ducky-logo.svg" alt="CyberDucky" width={80} />
            <h1 className="terms-title">Legal Agreement & Terms of Use</h1>
            <p className="terms-subtitle">Please read carefully before proceeding</p>
          </header>

          <div className="terms-content">
            <div className="warning-box">
              <h2>⚠️ IMPORTANT LEGAL NOTICE</h2>
              <p>
                By using this OSINT (Open Source Intelligence) platform, you acknowledge and agree 
                that you are solely responsible for ensuring your use complies with all applicable laws.
              </p>
            </div>

            <div className="terms-section">
              <h3>🔍 Permitted Use</h3>
              <ul>
                <li><strong>Self-Investigation:</strong> You may only investigate yourself or your own digital footprint</li>
                <li><strong>Explicit Authorization:</strong> You have explicit written permission from the subject being investigated</li>
                <li><strong>Legal Compliance:</strong> Your investigation complies with all local, state, federal, and international laws</li>
                <li><strong>Legitimate Purpose:</strong> The investigation serves a legitimate security, research, or educational purpose</li>
              </ul>
            </div>

            <div className="terms-section">
              <h3>🚫 Prohibited Activities</h3>
              <ul>
                <li>Investigating individuals without their explicit consent</li>
                <li>Stalking, harassment, or any form of malicious activity</li>
                <li>Corporate espionage or competitive intelligence gathering</li>
                <li>Any activity that violates privacy laws (GDPR, CCPA, etc.)</li>
                <li>Unauthorized access to private information or systems</li>
                <li>Using results for illegal purposes or discrimination</li>
              </ul>
            </div>

            <div className="terms-section">
              <h3>📋 Your Responsibilities</h3>
              <ul>
                <li><strong>Legal Compliance:</strong> Ensure all activities comply with applicable laws in your jurisdiction</li>
                <li><strong>Consent:</strong> Obtain proper authorization before investigating any individual</li>
                <li><strong>Data Protection:</strong> Handle any discovered information responsibly and securely</li>
                <li><strong>Ethical Use:</strong> Use this tool only for legitimate, ethical purposes</li>
              </ul>
            </div>

            <div className="terms-section">
              <h3>🛡️ Platform Disclaimers</h3>
              <ul>
                <li>This platform only collects publicly available information</li>
                <li>We do not store, retain, or share any investigation data</li>
                <li>All data is processed locally and temporarily</li>
                <li>We are not responsible for how you use the information obtained</li>
                <li>Results may contain inaccuracies or outdated information</li>
              </ul>
            </div>

            <div className="terms-section">
              <h3>⚖️ Legal Jurisdiction</h3>
              <p>
                You acknowledge that laws regarding privacy, data protection, and digital investigations 
                vary by jurisdiction. It is your responsibility to understand and comply with:
              </p>
              <ul>
                <li>Local privacy and data protection laws</li>
                <li>Federal regulations regarding digital investigations</li>
                <li>International laws if investigating across borders</li>
                <li>Professional codes of conduct if applicable to your role</li>
              </ul>
            </div>

            <div className="warning-box">
              <h3>🚨 Final Warning</h3>
              <p>
                <strong>Misuse of this platform may result in serious legal consequences including criminal charges.</strong>
                By proceeding, you certify that you understand these terms and will use this platform responsibly and legally.
              </p>
            </div>
          </div>

          <div className="terms-actions">
            <div className="checkbox-container">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={agreed}
                  onChange={(e) => setAgreed(e.target.checked)}
                  className="terms-checkbox"
                />
                <span className="checkmark"></span>
                I have read, understood, and agree to comply with all terms and legal requirements above
              </label>
            </div>

            <div className="button-group">
              <button 
                onClick={handleDecline}
                className="btn btn-decline"
              >
                Decline & Exit
              </button>
              <button 
                onClick={handleAccept}
                disabled={!agreed}
                className={`btn btn-accept ${!agreed ? 'disabled' : ''}`}
              >
                Accept & Continue
              </button>
            </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
