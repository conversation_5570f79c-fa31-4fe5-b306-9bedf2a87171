/* Modern Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2d3748;
  line-height: 1.6;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* App Container */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 4rem 0;
  margin-bottom: 3rem;
}

.logo-container {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-logo {
  width: 120px;
  height: 120px;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.main-logo:hover {
  transform: scale(1.05) rotate(5deg);
}

.hero-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  font-weight: 300;
  letter-spacing: 0.05em;
}

.hero-description {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Content Section */
.content-section {
  margin-bottom: 4rem;
}

/* Info Cards */
.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.info-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.info-card p {
  color: #4a5568;
  line-height: 1.6;
}

/* Form Section */
.form-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 600px;
  margin: 0 auto;
}

.form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
  margin-bottom: 1rem;
}

.form-description {
  color: #4a5568;
  text-align: center;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Form Styles */
.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form input {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  background: #ffffff;
  color: #2d3748;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.form input::placeholder {
  color: #a0aec0;
}

.form input[type="file"] {
  padding: 1rem;
  border: 2px dashed #cbd5e0;
  background: #f7fafc;
  cursor: pointer;
}

.form input[type="file"]:hover {
  border-color: #667eea;
  background: #edf2f7;
}

/* Beautiful Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  font-family: inherit;
  letter-spacing: 0.025em;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover:before {
  left: 100%;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.btn:active {
  transform: translateY(1px);
}

/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Accept Button */
.btn-accept {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.btn-accept:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.btn-accept.disabled {
  background: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Decline Button */
.btn-decline {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

.btn-decline:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
}

/* Form Submit Button */
.form button[type="submit"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.form button[type="submit"]:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.form button[type="submit"]:hover:before {
  left: 100%;
}

.form button[type="submit"]:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.form button[type="submit"]:disabled {
  background: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Section */
.loading-section {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-section h2 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.loading-section p {
  color: #4a5568;
  font-size: 1.1rem;
}

/* Footer Section */
.footer-section {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.legal-notice {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.legal-notice h4 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.legal-notice p {
  color: #4a5568;
  line-height: 1.6;
  font-size: 0.95rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.footer-link {
  background: none;
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: rgba(255, 255, 255, 0.9);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.footer-link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 1);
  color: white;
}

/* Terms Page Styles */
.terms-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.terms-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #e2e8f0;
}

.terms-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  margin-bottom: 1rem;
}

.terms-subtitle {
  color: #4a5568;
  font-size: 1.2rem;
  font-weight: 300;
}

.terms-content {
  margin-bottom: 3rem;
}

.warning-box {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  border: 2px solid #f56565;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.warning-box h2, .warning-box h3 {
  color: #c53030;
  margin-bottom: 1rem;
}

.warning-box p {
  color: #742a2a;
  font-weight: 500;
  line-height: 1.6;
}

.terms-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f7fafc;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.terms-section h3 {
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.terms-section ul {
  list-style: none;
  padding-left: 0;
}

.terms-section li {
  color: #4a5568;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
  line-height: 1.6;
}

.terms-section li:before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.terms-section p {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.terms-actions {
  border-top: 2px solid #e2e8f0;
  padding-top: 2rem;
}

.checkbox-container {
  margin-bottom: 2rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  color: #2d3748;
  font-weight: 500;
  line-height: 1.6;
}

.terms-checkbox {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .hero-section {
    padding: 2rem 0;
  }

  .info-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-section {
    padding: 2rem;
  }

  .terms-container {
    padding: 1rem;
  }

  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .main-logo {
    width: 80px;
    height: 80px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .info-card {
    padding: 1.5rem;
  }

  .form-section {
    padding: 1.5rem;
  }
}

/* Legacy styles for results page compatibility */
.scan-status {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid #667eea;
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.status-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.status-label {
  color: #4a5568;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.status-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
}

.scan-results {
  margin: 2rem 0;
}

.finding-group {
  margin: 2rem 0;
}

.group-title {
  color: #2d3748;
  border-bottom: 2px solid #667eea;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  font-weight: 600;
}

.findings-list {
  display: grid;
  gap: 1rem;
}

.finding-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.finding-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.finding-header {
  margin-bottom: 1rem;
}

.finding-title {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.finding-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.source-badge, .platform-badge, .risk-badge, .confidence-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: bold;
}

.source-badge {
  background: #6c757d;
  color: white;
}

.platform-badge {
  background: #667eea;
  color: white;
}

.confidence-badge {
  background: #48bb78;
  color: white;
}

.finding-description {
  color: #4a5568;
  margin: 1rem 0;
  line-height: 1.5;
}

.finding-url {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
}

.finding-url:hover {
  text-decoration: underline;
}

.finding-timestamp {
  color: #718096;
  font-size: 0.8rem;
  margin-top: 1rem;
}

.report-download {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid #667eea;
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.download-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.download-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #667eea;
}

.no-results {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  margin: 2rem 0;
  color: #4a5568;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: rgba(254, 215, 215, 0.95);
  border: 2px solid #f56565;
  border-radius: 12px;
  margin: 2rem 0;
  color: #c53030;
}

/* Container for legacy compatibility */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* Authentication Styles */
.header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-section {
  margin: 1rem 0;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #00ffe7;
}

.dashboard-link {
  color: #00ffe7;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid #00ffe7;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.dashboard-link:hover {
  background: #00ffe7;
  color: #0f0f1e;
}

.logout-btn, .auth-toggle-btn {
  background: transparent;
  border: 1px solid #00ffe7;
  color: #00ffe7;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover, .auth-toggle-btn:hover {
  background: #00ffe7;
  color: #0f0f1e;
}

.auth-form {
  background: #1a1a2e;
  border: 2px solid #00ffe7;
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.auth-form h2 {
  color: #00ffe7;
  text-align: center;
  margin-bottom: 1.5rem;
}

.auth-form input {
  width: 100%;
  padding: 0.75rem;
  margin: 0.5rem 0;
  background: #0f0f1e;
  border: 1px solid #333;
  border-radius: 4px;
  color: #fff;
  box-sizing: border-box;
}

.auth-form input:focus {
  outline: none;
  border-color: #00ffe7;
}

.auth-form button[type="submit"] {
  width: 100%;
  background: #00ffe7;
  color: #0f0f1e;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.auth-form button[type="submit"]:hover:not(:disabled) {
  background: #00d4c4;
}

.auth-form button[type="submit"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-switch {
  text-align: center;
  margin-top: 1rem;
  color: #ccc;
}

.auth-skip {
  text-align: center;
  margin-top: 0.5rem;
  color: #888;
}

.link-button {
  background: none;
  border: none;
  color: #00ffe7;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-button:hover {
  color: #00d4c4;
}

.message {
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  text-align: center;
}

.message.error {
  background: #2e1a1a;
  border: 1px solid #dc3545;
  color: #ff6b6b;
}

.message.success {
  background: #1a2e1a;
  border: 1px solid #28a745;
  color: #6bff6b;
}

/* Footer */
.footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #333;
  text-align: center;
  color: #888;
  font-size: 0.9rem;
}

/* Dashboard Styles */
.nav-links {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.nav-link {
  background: transparent;
  border: 1px solid #00ffe7;
  color: #00ffe7;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: #00ffe7;
  color: #0f0f1e;
}

.dashboard-content {
  margin: 2rem 0;
}

.dashboard-content h2 {
  color: #00ffe7;
  border-bottom: 2px solid #00ffe7;
  padding-bottom: 0.5rem;
  margin-bottom: 2rem;
}

.no-searches {
  text-align: center;
  padding: 2rem;
  background: #1a1a2e;
  border: 2px solid #333;
  border-radius: 8px;
  color: #ccc;
}

.no-searches a {
  color: #00ffe7;
  text-decoration: none;
}

.no-searches a:hover {
  text-decoration: underline;
}

.search-history {
  display: grid;
  gap: 1rem;
}

.search-item {
  background: #1a1a2e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.search-item:hover {
  border-color: #00ffe7;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.search-header h3 {
  color: #00ffe7;
  margin: 0;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.status-completed {
  background: #28a745;
  color: white;
}

.status-running {
  background: #ffc107;
  color: #000;
}

.status-pending {
  background: #6c757d;
  color: white;
}

.status-failed {
  background: #dc3545;
  color: white;
}

.search-details {
  margin-bottom: 1rem;
}

.search-meta {
  display: flex;
  gap: 1rem;
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.search-stats {
  display: flex;
  gap: 1rem;
  color: #ccc;
  font-size: 0.9rem;
}

.search-stats .risk-low {
  color: #28a745;
}

.search-stats .risk-medium {
  color: #ffc107;
}

.search-stats .risk-high {
  color: #fd7e14;
}

.search-stats .risk-critical {
  color: #dc3545;
}

.search-actions {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  background: #00ffe7;
  color: #0f0f1e;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: #00d4c4;
  transform: translateY(-1px);
}
